<?xml version="1.0" encoding="utf-8" ?>
<FinancialFunctions>
	<!-- ACCBANDS -->
	<FinancialFunction>
		<Abbreviation>ACCBANDS</Abbreviation>
		<CamelCaseName>Accbands</CamelCaseName>
		<ShortDescription>Acceleration Bands</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>20</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealUpperBand</Name>
				<Flags>
					<Flag>Upper Limit</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealMiddleBand</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealLowerBand</Name>
				<Flags>
					<Flag>Lower Limit</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ACOS -->
	<FinancialFunction>
		<Abbreviation>ACOS</Abbreviation>
		<CamelCaseName>Acos</CamelCaseName>
		<ShortDescription>Vector Trigonometric ACos</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- AD -->
	<FinancialFunction>
		<Abbreviation>AD</Abbreviation>
		<CamelCaseName>Ad</CamelCaseName>
		<ShortDescription>Chaikin A/D Line</ShortDescription>
		<GroupId>Volume Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Volume</Type>
				<Name>Volume</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ADD -->
	<FinancialFunction>
		<Abbreviation>ADD</Abbreviation>
		<CamelCaseName>Add</CamelCaseName>
		<ShortDescription>Vector Arithmetic Add</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ADOSC -->
	<FinancialFunction>
		<Abbreviation>ADOSC</Abbreviation>
		<CamelCaseName>AdOsc</CamelCaseName>
		<ShortDescription>Chaikin A/D Oscillator</ShortDescription>
		<GroupId>Volume Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Volume</Type>
				<Name>Volume</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Period</Name>
				<ShortDescription>Number of period for the fast MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>3</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Period</Name>
				<ShortDescription>Number of period for the slow MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ADX -->
	<FinancialFunction>
		<Abbreviation>ADX</Abbreviation>
		<CamelCaseName>Adx</CamelCaseName>
		<ShortDescription>Average Directional Movement Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ADXR -->
	<FinancialFunction>
		<Abbreviation>ADXR</Abbreviation>
		<CamelCaseName>Adxr</CamelCaseName>
		<ShortDescription>Average Directional Movement Index Rating</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- APO -->
	<FinancialFunction>
		<Abbreviation>APO</Abbreviation>
		<CamelCaseName>Apo</CamelCaseName>
		<ShortDescription>Absolute Price Oscillator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Period</Name>
				<ShortDescription>Number of period for the fast MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>12</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Period</Name>
				<ShortDescription>Number of period for the slow MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>26</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>MA Type</Name>
				<ShortDescription>Type of Moving Average</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- AROON -->
	<FinancialFunction>
		<Abbreviation>AROON</Abbreviation>
		<CamelCaseName>Aroon</CamelCaseName>
		<ShortDescription>Aroon</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outAroonDown</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outAroonUp</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- AROONOSC -->
	<FinancialFunction>
		<Abbreviation>AROONOSC</Abbreviation>
		<CamelCaseName>AroonOsc</CamelCaseName>
		<ShortDescription>Aroon Oscillator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ASIN -->
	<FinancialFunction>
		<Abbreviation>ASIN</Abbreviation>
		<CamelCaseName>Asin</CamelCaseName>
		<ShortDescription>Vector Trigonometric ASin</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ATAN -->
	<FinancialFunction>
		<Abbreviation>ATAN</Abbreviation>
		<CamelCaseName>Atan</CamelCaseName>
		<ShortDescription>Vector Trigonometric ATan</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ATR -->
	<FinancialFunction>
		<Abbreviation>ATR</Abbreviation>
		<CamelCaseName>Atr</CamelCaseName>
		<ShortDescription>Average True Range</ShortDescription>
		<GroupId>Volatility Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- AVGPRICE -->
	<FinancialFunction>
		<Abbreviation>AVGPRICE</Abbreviation>
		<CamelCaseName>AvgPrice</CamelCaseName>
		<ShortDescription>Average Price</ShortDescription>
		<GroupId>Price Transform</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- AVGDEV -->
	<FinancialFunction>
		<Abbreviation>AVGDEV</Abbreviation>
		<CamelCaseName>AvgDev</CamelCaseName>
		<ShortDescription>Average Deviation</ShortDescription>
		<GroupId>Price Transform</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- BBANDS -->
	<FinancialFunction>
		<Abbreviation>BBANDS</Abbreviation>
		<CamelCaseName>Bbands</CamelCaseName>
		<ShortDescription>Bollinger Bands</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Deviations up</Name>
				<ShortDescription>Deviation multiplier for upper band</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>-3.000000e+37</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>-2.000000e+0</SuggestedStart>
					<SuggestedEnd>2.000000e+0</SuggestedEnd>
					<SuggestedIncrement>2.000000e-1</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e+0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Deviations down</Name>
				<ShortDescription>Deviation multiplier for lower band</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>-3.000000e+37</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>-2.000000e+0</SuggestedStart>
					<SuggestedEnd>2.000000e+0</SuggestedEnd>
					<SuggestedIncrement>2.000000e-1</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e+0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>MA Type</Name>
				<ShortDescription>Type of Moving Average</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealUpperBand</Name>
				<Flags>
					<Flag>Upper Limit</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealMiddleBand</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outRealLowerBand</Name>
				<Flags>
					<Flag>Lower Limit</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- BETA -->
	<FinancialFunction>
		<Abbreviation>BETA</Abbreviation>
		<CamelCaseName>Beta</CamelCaseName>
		<ShortDescription>Beta</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- BOP -->
	<FinancialFunction>
		<Abbreviation>BOP</Abbreviation>
		<CamelCaseName>Bop</CamelCaseName>
		<ShortDescription>Balance Of Power</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CCI -->
	<FinancialFunction>
		<Abbreviation>CCI</Abbreviation>
		<CamelCaseName>Cci</CamelCaseName>
		<ShortDescription>Commodity Channel Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL2CROWS -->
	<FinancialFunction>
		<Abbreviation>CDL2CROWS</Abbreviation>
		<CamelCaseName>Cdl2Crows</CamelCaseName>
		<ShortDescription>Two Crows</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3BLACKCROWS -->
	<FinancialFunction>
		<Abbreviation>CDL3BLACKCROWS</Abbreviation>
		<CamelCaseName>Cdl3BlackCrows</CamelCaseName>
		<ShortDescription>Three Black Crows</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3INSIDE -->
	<FinancialFunction>
		<Abbreviation>CDL3INSIDE</Abbreviation>
		<CamelCaseName>Cdl3Inside</CamelCaseName>
		<ShortDescription>Three Inside Up/Down</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3LINESTRIKE -->
	<FinancialFunction>
		<Abbreviation>CDL3LINESTRIKE</Abbreviation>
		<CamelCaseName>Cdl3LineStrike</CamelCaseName>
		<ShortDescription>Three-Line Strike </ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3OUTSIDE -->
	<FinancialFunction>
		<Abbreviation>CDL3OUTSIDE</Abbreviation>
		<CamelCaseName>Cdl3Outside</CamelCaseName>
		<ShortDescription>Three Outside Up/Down</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3STARSINSOUTH -->
	<FinancialFunction>
		<Abbreviation>CDL3STARSINSOUTH</Abbreviation>
		<CamelCaseName>Cdl3StarsInSouth</CamelCaseName>
		<ShortDescription>Three Stars In The South</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDL3WHITESOLDIERS -->
	<FinancialFunction>
		<Abbreviation>CDL3WHITESOLDIERS</Abbreviation>
		<CamelCaseName>Cdl3WhiteSoldiers</CamelCaseName>
		<ShortDescription>Three Advancing White Soldiers</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLABANDONEDBABY -->
	<FinancialFunction>
		<Abbreviation>CDLABANDONEDBABY</Abbreviation>
		<CamelCaseName>CdlAbandonedBaby</CamelCaseName>
		<ShortDescription>Abandoned Baby</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>3.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLADVANCEBLOCK -->
	<FinancialFunction>
		<Abbreviation>CDLADVANCEBLOCK</Abbreviation>
		<CamelCaseName>CdlAdvanceBlock</CamelCaseName>
		<ShortDescription>Advance Block</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLBELTHOLD -->
	<FinancialFunction>
		<Abbreviation>CDLBELTHOLD</Abbreviation>
		<CamelCaseName>CdlBeltHold</CamelCaseName>
		<ShortDescription>Belt-hold</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLBREAKAWAY -->
	<FinancialFunction>
		<Abbreviation>CDLBREAKAWAY</Abbreviation>
		<CamelCaseName>CdlBreakaway</CamelCaseName>
		<ShortDescription>Breakaway</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLCLOSINGMARUBOZU -->
	<FinancialFunction>
		<Abbreviation>CDLCLOSINGMARUBOZU</Abbreviation>
		<CamelCaseName>CdlClosingMarubozu</CamelCaseName>
		<ShortDescription>Closing Marubozu</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLCONCEALBABYSWALL -->
	<FinancialFunction>
		<Abbreviation>CDLCONCEALBABYSWALL</Abbreviation>
		<CamelCaseName>CdlConcealBabysWall</CamelCaseName>
		<ShortDescription>Concealing Baby Swallow</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLCOUNTERATTACK -->
	<FinancialFunction>
		<Abbreviation>CDLCOUNTERATTACK</Abbreviation>
		<CamelCaseName>CdlCounterAttack</CamelCaseName>
		<ShortDescription>Counterattack</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLDARKCLOUDCOVER -->
	<FinancialFunction>
		<Abbreviation>CDLDARKCLOUDCOVER</Abbreviation>
		<CamelCaseName>CdlDarkCloudCover</CamelCaseName>
		<ShortDescription>Dark Cloud Cover</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>5.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLDOJI -->
	<FinancialFunction>
		<Abbreviation>CDLDOJI</Abbreviation>
		<CamelCaseName>CdlDoji</CamelCaseName>
		<ShortDescription>Doji</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLDOJISTAR -->
	<FinancialFunction>
		<Abbreviation>CDLDOJISTAR</Abbreviation>
		<CamelCaseName>CdlDojiStar</CamelCaseName>
		<ShortDescription>Doji Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLDRAGONFLYDOJI -->
	<FinancialFunction>
		<Abbreviation>CDLDRAGONFLYDOJI</Abbreviation>
		<CamelCaseName>CdlDragonflyDoji</CamelCaseName>
		<ShortDescription>Dragonfly Doji</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLENGULFING -->
	<FinancialFunction>
		<Abbreviation>CDLENGULFING</Abbreviation>
		<CamelCaseName>CdlEngulfing</CamelCaseName>
		<ShortDescription>Engulfing Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLEVENINGDOJISTAR -->
	<FinancialFunction>
		<Abbreviation>CDLEVENINGDOJISTAR</Abbreviation>
		<CamelCaseName>CdlEveningDojiStar</CamelCaseName>
		<ShortDescription>Evening Doji Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>3.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLEVENINGSTAR -->
	<FinancialFunction>
		<Abbreviation>CDLEVENINGSTAR</Abbreviation>
		<CamelCaseName>CdlEveningStar</CamelCaseName>
		<ShortDescription>Evening Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>3.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLGAPSIDESIDEWHITE -->
	<FinancialFunction>
		<Abbreviation>CDLGAPSIDESIDEWHITE</Abbreviation>
		<CamelCaseName>CdlGapSideSideWhite</CamelCaseName>
		<ShortDescription>Up/Down-gap side-by-side white lines</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLGRAVESTONEDOJI -->
	<FinancialFunction>
		<Abbreviation>CDLGRAVESTONEDOJI</Abbreviation>
		<CamelCaseName>CdlGravestoneDoji</CamelCaseName>
		<ShortDescription>Gravestone Doji</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHAMMER -->
	<FinancialFunction>
		<Abbreviation>CDLHAMMER</Abbreviation>
		<CamelCaseName>CdlHammer</CamelCaseName>
		<ShortDescription>Hammer</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHANGINGMAN -->
	<FinancialFunction>
		<Abbreviation>CDLHANGINGMAN</Abbreviation>
		<CamelCaseName>CdlHangingMan</CamelCaseName>
		<ShortDescription>Hanging Man</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHARAMI -->
	<FinancialFunction>
		<Abbreviation>CDLHARAMI</Abbreviation>
		<CamelCaseName>CdlHarami</CamelCaseName>
		<ShortDescription>Harami Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHARAMICROSS -->
	<FinancialFunction>
		<Abbreviation>CDLHARAMICROSS</Abbreviation>
		<CamelCaseName>CdlHaramiCross</CamelCaseName>
		<ShortDescription>Harami Cross Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHIGHWAVE -->
	<FinancialFunction>
		<Abbreviation>CDLHIGHWAVE</Abbreviation>
		<CamelCaseName>CdlHignWave</CamelCaseName>
		<ShortDescription>High-Wave Candle</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHIKKAKE -->
	<FinancialFunction>
		<Abbreviation>CDLHIKKAKE</Abbreviation>
		<CamelCaseName>CdlHikkake</CamelCaseName>
		<ShortDescription>Hikkake Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHIKKAKEMOD -->
	<FinancialFunction>
		<Abbreviation>CDLHIKKAKEMOD</Abbreviation>
		<CamelCaseName>CdlHikkakeMod</CamelCaseName>
		<ShortDescription>Modified Hikkake Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLHOMINGPIGEON -->
	<FinancialFunction>
		<Abbreviation>CDLHOMINGPIGEON</Abbreviation>
		<CamelCaseName>CdlHomingPigeon</CamelCaseName>
		<ShortDescription>Homing Pigeon</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLIDENTICAL3CROWS -->
	<FinancialFunction>
		<Abbreviation>CDLIDENTICAL3CROWS</Abbreviation>
		<CamelCaseName>CdlIdentical3Crows</CamelCaseName>
		<ShortDescription>Identical Three Crows</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLINNECK -->
	<FinancialFunction>
		<Abbreviation>CDLINNECK</Abbreviation>
		<CamelCaseName>CdlInNeck</CamelCaseName>
		<ShortDescription>In-Neck Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLINVERTEDHAMMER -->
	<FinancialFunction>
		<Abbreviation>CDLINVERTEDHAMMER</Abbreviation>
		<CamelCaseName>CdlInvertedHammer</CamelCaseName>
		<ShortDescription>Inverted Hammer</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLKICKING -->
	<FinancialFunction>
		<Abbreviation>CDLKICKING</Abbreviation>
		<CamelCaseName>CdlKicking</CamelCaseName>
		<ShortDescription>Kicking</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLKICKINGBYLENGTH -->
	<FinancialFunction>
		<Abbreviation>CDLKICKINGBYLENGTH</Abbreviation>
		<CamelCaseName>CdlKickingByLength</CamelCaseName>
		<ShortDescription>Kicking - bull/bear determined by the longer marubozu</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLLADDERBOTTOM -->
	<FinancialFunction>
		<Abbreviation>CDLLADDERBOTTOM</Abbreviation>
		<CamelCaseName>CdlLadderBottom</CamelCaseName>
		<ShortDescription>Ladder Bottom</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLLONGLEGGEDDOJI -->
	<FinancialFunction>
		<Abbreviation>CDLLONGLEGGEDDOJI</Abbreviation>
		<CamelCaseName>CdlLongLeggedDoji</CamelCaseName>
		<ShortDescription>Long Legged Doji</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLLONGLINE -->
	<FinancialFunction>
		<Abbreviation>CDLLONGLINE</Abbreviation>
		<CamelCaseName>CdlLongLine</CamelCaseName>
		<ShortDescription>Long Line Candle</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLMARUBOZU -->
	<FinancialFunction>
		<Abbreviation>CDLMARUBOZU</Abbreviation>
		<CamelCaseName>CdlMarubozu</CamelCaseName>
		<ShortDescription>Marubozu</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLMATCHINGLOW -->
	<FinancialFunction>
		<Abbreviation>CDLMATCHINGLOW</Abbreviation>
		<CamelCaseName>CdlMatchingLow</CamelCaseName>
		<ShortDescription>Matching Low</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLMATHOLD -->
	<FinancialFunction>
		<Abbreviation>CDLMATHOLD</Abbreviation>
		<CamelCaseName>CdlMatHold</CamelCaseName>
		<ShortDescription>Mat Hold</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>5.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLMORNINGDOJISTAR -->
	<FinancialFunction>
		<Abbreviation>CDLMORNINGDOJISTAR</Abbreviation>
		<CamelCaseName>CdlMorningDojiStar</CamelCaseName>
		<ShortDescription>Morning Doji Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>3.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLMORNINGSTAR -->
	<FinancialFunction>
		<Abbreviation>CDLMORNINGSTAR</Abbreviation>
		<CamelCaseName>CdlMorningStar</CamelCaseName>
		<ShortDescription>Morning Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Penetration</Name>
				<ShortDescription>Percentage of penetration of a candle within another candle</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>0</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>3.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLONNECK -->
	<FinancialFunction>
		<Abbreviation>CDLONNECK</Abbreviation>
		<CamelCaseName>CdlOnNeck</CamelCaseName>
		<ShortDescription>On-Neck Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLPIERCING -->
	<FinancialFunction>
		<Abbreviation>CDLPIERCING</Abbreviation>
		<CamelCaseName>CdlPiercing</CamelCaseName>
		<ShortDescription>Piercing Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLRICKSHAWMAN -->
	<FinancialFunction>
		<Abbreviation>CDLRICKSHAWMAN</Abbreviation>
		<CamelCaseName>CdlRickshawMan</CamelCaseName>
		<ShortDescription>Rickshaw Man</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLRISEFALL3METHODS -->
	<FinancialFunction>
		<Abbreviation>CDLRISEFALL3METHODS</Abbreviation>
		<CamelCaseName>CdlRiseFall3Methods</CamelCaseName>
		<ShortDescription>Rising/Falling Three Methods</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSEPARATINGLINES -->
	<FinancialFunction>
		<Abbreviation>CDLSEPARATINGLINES</Abbreviation>
		<CamelCaseName>CdlSeperatingLines</CamelCaseName>
		<ShortDescription>Separating Lines</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSHOOTINGSTAR -->
	<FinancialFunction>
		<Abbreviation>CDLSHOOTINGSTAR</Abbreviation>
		<CamelCaseName>CdlShootingStar</CamelCaseName>
		<ShortDescription>Shooting Star</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSHORTLINE -->
	<FinancialFunction>
		<Abbreviation>CDLSHORTLINE</Abbreviation>
		<CamelCaseName>CdlShortLine</CamelCaseName>
		<ShortDescription>Short Line Candle</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSPINNINGTOP -->
	<FinancialFunction>
		<Abbreviation>CDLSPINNINGTOP</Abbreviation>
		<CamelCaseName>CdlSpinningTop</CamelCaseName>
		<ShortDescription>Spinning Top</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSTALLEDPATTERN -->
	<FinancialFunction>
		<Abbreviation>CDLSTALLEDPATTERN</Abbreviation>
		<CamelCaseName>CdlStalledPattern</CamelCaseName>
		<ShortDescription>Stalled Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLSTICKSANDWICH -->
	<FinancialFunction>
		<Abbreviation>CDLSTICKSANDWICH</Abbreviation>
		<CamelCaseName>CdlStickSandwich</CamelCaseName>
		<ShortDescription>Stick Sandwich</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLTAKURI -->
	<FinancialFunction>
		<Abbreviation>CDLTAKURI</Abbreviation>
		<CamelCaseName>CdlTakuri</CamelCaseName>
		<ShortDescription>Takuri (Dragonfly Doji with very long lower shadow)</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLTASUKIGAP -->
	<FinancialFunction>
		<Abbreviation>CDLTASUKIGAP</Abbreviation>
		<CamelCaseName>CdlTasukiGap</CamelCaseName>
		<ShortDescription>Tasuki Gap</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLTHRUSTING -->
	<FinancialFunction>
		<Abbreviation>CDLTHRUSTING</Abbreviation>
		<CamelCaseName>CdlThrusting</CamelCaseName>
		<ShortDescription>Thrusting Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLTRISTAR -->
	<FinancialFunction>
		<Abbreviation>CDLTRISTAR</Abbreviation>
		<CamelCaseName>CdlTristar</CamelCaseName>
		<ShortDescription>Tristar Pattern</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLUNIQUE3RIVER -->
	<FinancialFunction>
		<Abbreviation>CDLUNIQUE3RIVER</Abbreviation>
		<CamelCaseName>CdlUnique3River</CamelCaseName>
		<ShortDescription>Unique 3 River</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLUPSIDEGAP2CROWS -->
	<FinancialFunction>
		<Abbreviation>CDLUPSIDEGAP2CROWS</Abbreviation>
		<CamelCaseName>CdlUpsideGap2Crows</CamelCaseName>
		<ShortDescription>Upside Gap Two Crows</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CDLXSIDEGAP3METHODS -->
	<FinancialFunction>
		<Abbreviation>CDLXSIDEGAP3METHODS</Abbreviation>
		<CamelCaseName>CdlXSideGap3Methods</CamelCaseName>
		<ShortDescription>Upside/Downside Gap Three Methods</ShortDescription>
		<GroupId>Pattern Recognition</GroupId>
		<Flags>
			<Flag>Candlestick</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CEIL -->
	<FinancialFunction>
		<Abbreviation>CEIL</Abbreviation>
		<CamelCaseName>Ceil</CamelCaseName>
		<ShortDescription>Vector Ceil</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CMO -->
	<FinancialFunction>
		<Abbreviation>CMO</Abbreviation>
		<CamelCaseName>Cmo</CamelCaseName>
		<ShortDescription>Chande Momentum Oscillator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- CORREL -->
	<FinancialFunction>
		<Abbreviation>CORREL</Abbreviation>
		<CamelCaseName>Correl</CamelCaseName>
		<ShortDescription>Pearson&apos;s Correlation Coefficient (r)</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- COS -->
	<FinancialFunction>
		<Abbreviation>COS</Abbreviation>
		<CamelCaseName>Cos</CamelCaseName>
		<ShortDescription>Vector Trigonometric Cos</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- COSH -->
	<FinancialFunction>
		<Abbreviation>COSH</Abbreviation>
		<CamelCaseName>Cosh</CamelCaseName>
		<ShortDescription>Vector Trigonometric Cosh</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- DEMA -->
	<FinancialFunction>
		<Abbreviation>DEMA</Abbreviation>
		<CamelCaseName>Dema</CamelCaseName>
		<ShortDescription>Double Exponential Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- DIV -->
	<FinancialFunction>
		<Abbreviation>DIV</Abbreviation>
		<CamelCaseName>Div</CamelCaseName>
		<ShortDescription>Vector Arithmetic Div</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- DX -->
	<FinancialFunction>
		<Abbreviation>DX</Abbreviation>
		<CamelCaseName>Dx</CamelCaseName>
		<ShortDescription>Directional Movement Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- EMA -->
	<FinancialFunction>
		<Abbreviation>EMA</Abbreviation>
		<CamelCaseName>Ema</CamelCaseName>
		<ShortDescription>Exponential Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- EXP -->
	<FinancialFunction>
		<Abbreviation>EXP</Abbreviation>
		<CamelCaseName>Exp</CamelCaseName>
		<ShortDescription>Vector Arithmetic Exp</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- FLOOR -->
	<FinancialFunction>
		<Abbreviation>FLOOR</Abbreviation>
		<CamelCaseName>Floor</CamelCaseName>
		<ShortDescription>Vector Floor</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_DCPERIOD -->
	<FinancialFunction>
		<Abbreviation>HT_DCPERIOD</Abbreviation>
		<CamelCaseName>HtDcPeriod</CamelCaseName>
		<ShortDescription>Hilbert Transform - Dominant Cycle Period</ShortDescription>
		<GroupId>Cycle Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_DCPHASE -->
	<FinancialFunction>
		<Abbreviation>HT_DCPHASE</Abbreviation>
		<CamelCaseName>HtDcPhase</CamelCaseName>
		<ShortDescription>Hilbert Transform - Dominant Cycle Phase</ShortDescription>
		<GroupId>Cycle Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_PHASOR -->
	<FinancialFunction>
		<Abbreviation>HT_PHASOR</Abbreviation>
		<CamelCaseName>HtPhasor</CamelCaseName>
		<ShortDescription>Hilbert Transform - Phasor Components</ShortDescription>
		<GroupId>Cycle Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outInPhase</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outQuadrature</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_SINE -->
	<FinancialFunction>
		<Abbreviation>HT_SINE</Abbreviation>
		<CamelCaseName>HtSine</CamelCaseName>
		<ShortDescription>Hilbert Transform - SineWave</ShortDescription>
		<GroupId>Cycle Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outSine</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outLeadSine</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_TRENDLINE -->
	<FinancialFunction>
		<Abbreviation>HT_TRENDLINE</Abbreviation>
		<CamelCaseName>HtTrendline</CamelCaseName>
		<ShortDescription>Hilbert Transform - Instantaneous Trendline</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- HT_TRENDMODE -->
	<FinancialFunction>
		<Abbreviation>HT_TRENDMODE</Abbreviation>
		<CamelCaseName>HtTrendMode</CamelCaseName>
		<ShortDescription>Hilbert Transform - Trend vs Cycle Mode</ShortDescription>
		<GroupId>Cycle Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- IMI -->
	<FinancialFunction>
		<Abbreviation>IMI</Abbreviation>
		<CamelCaseName>Imi</CamelCaseName>
		<ShortDescription>Intraday Momentum Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Open</Type>
				<Name>Open</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- KAMA -->
	<FinancialFunction>
		<Abbreviation>KAMA</Abbreviation>
		<CamelCaseName>Kama</CamelCaseName>
		<ShortDescription>Kaufman Adaptive Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LINEARREG -->
	<FinancialFunction>
		<Abbreviation>LINEARREG</Abbreviation>
		<CamelCaseName>LinearReg</CamelCaseName>
		<ShortDescription>Linear Regression</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LINEARREG_ANGLE -->
	<FinancialFunction>
		<Abbreviation>LINEARREG_ANGLE</Abbreviation>
		<CamelCaseName>LinearRegAngle</CamelCaseName>
		<ShortDescription>Linear Regression Angle</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LINEARREG_INTERCEPT -->
	<FinancialFunction>
		<Abbreviation>LINEARREG_INTERCEPT</Abbreviation>
		<CamelCaseName>LinearRegIntercept</CamelCaseName>
		<ShortDescription>Linear Regression Intercept</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LINEARREG_SLOPE -->
	<FinancialFunction>
		<Abbreviation>LINEARREG_SLOPE</Abbreviation>
		<CamelCaseName>LinearRegSlope</CamelCaseName>
		<ShortDescription>Linear Regression Slope</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LN -->
	<FinancialFunction>
		<Abbreviation>LN</Abbreviation>
		<CamelCaseName>Ln</CamelCaseName>
		<ShortDescription>Vector Log Natural</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- LOG10 -->
	<FinancialFunction>
		<Abbreviation>LOG10</Abbreviation>
		<CamelCaseName>Log10</CamelCaseName>
		<ShortDescription>Vector Log10</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MA -->
	<FinancialFunction>
		<Abbreviation>MA</Abbreviation>
		<CamelCaseName>MovingAverage</CamelCaseName>
		<ShortDescription>Moving average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>MA Type</Name>
				<ShortDescription>Type of Moving Average</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MACD -->
	<FinancialFunction>
		<Abbreviation>MACD</Abbreviation>
		<CamelCaseName>Macd</CamelCaseName>
		<ShortDescription>Moving Average Convergence/Divergence</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Period</Name>
				<ShortDescription>Number of period for the fast MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>12</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Period</Name>
				<ShortDescription>Number of period for the slow MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>26</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Signal Period</Name>
				<ShortDescription>Smoothing for the signal line (nb of period)</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>9</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACD</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDSignal</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDHist</Name>
				<Flags>
					<Flag>Histogram</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MACDEXT -->
	<FinancialFunction>
		<Abbreviation>MACDEXT</Abbreviation>
		<CamelCaseName>MacdExt</CamelCaseName>
		<ShortDescription>MACD with controllable MA type</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Period</Name>
				<ShortDescription>Number of period for the fast MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>12</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast MA</Name>
				<ShortDescription>Type of Moving Average for fast MA</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Period</Name>
				<ShortDescription>Number of period for the slow MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>26</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow MA</Name>
				<ShortDescription>Type of Moving Average for slow MA</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Signal Period</Name>
				<ShortDescription>Smoothing for the signal line (nb of period)</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>9</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Signal MA</Name>
				<ShortDescription>Type of Moving Average for signal line</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACD</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDSignal</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDHist</Name>
				<Flags>
					<Flag>Histogram</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MACDFIX -->
	<FinancialFunction>
		<Abbreviation>MACDFIX</Abbreviation>
		<CamelCaseName>MacdFix</CamelCaseName>
		<ShortDescription>Moving Average Convergence/Divergence Fix 12/26</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Signal Period</Name>
				<ShortDescription>Smoothing for the signal line (nb of period)</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>9</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACD</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDSignal</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMACDHist</Name>
				<Flags>
					<Flag>Histogram</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MAMA -->
	<FinancialFunction>
		<Abbreviation>MAMA</Abbreviation>
		<CamelCaseName>Mama</CamelCaseName>
		<ShortDescription>MESA Adaptive Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Limit</Name>
				<ShortDescription>Upper limit use in the adaptive algorithm</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>1.000000e-2</Minimum>
					<Maximum>9.900000e-1</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>2.100000e-1</SuggestedStart>
					<SuggestedEnd>8.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>5.000000e-1</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Limit</Name>
				<ShortDescription>Lower limit use in the adaptive algorithm</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>1.000000e-2</Minimum>
					<Maximum>9.900000e-1</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>6.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>5.000000e-2</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMAMA</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outFAMA</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MAVP -->
	<FinancialFunction>
		<Abbreviation>MAVP</Abbreviation>
		<CamelCaseName>MovingAverageVariablePeriod</CamelCaseName>
		<ShortDescription>Moving average with variable period</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inPeriods</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Minimum Period</Name>
				<ShortDescription>Value less than minimum will be changed to Minimum period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Maximum Period</Name>
				<ShortDescription>Value higher than maximum will be changed to Maximum period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>MA Type</Name>
				<ShortDescription>Type of Moving Average</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MAX -->
	<FinancialFunction>
		<Abbreviation>MAX</Abbreviation>
		<CamelCaseName>Max</CamelCaseName>
		<ShortDescription>Highest value over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MAXINDEX -->
	<FinancialFunction>
		<Abbreviation>MAXINDEX</Abbreviation>
		<CamelCaseName>MaxIndex</CamelCaseName>
		<ShortDescription>Index of highest value over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MEDPRICE -->
	<FinancialFunction>
		<Abbreviation>MEDPRICE</Abbreviation>
		<CamelCaseName>MedPrice</CamelCaseName>
		<ShortDescription>Median Price</ShortDescription>
		<GroupId>Price Transform</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MFI -->
	<FinancialFunction>
		<Abbreviation>MFI</Abbreviation>
		<CamelCaseName>Mfi</CamelCaseName>
		<ShortDescription>Money Flow Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Volume</Type>
				<Name>Volume</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MIDPOINT -->
	<FinancialFunction>
		<Abbreviation>MIDPOINT</Abbreviation>
		<CamelCaseName>MidPoint</CamelCaseName>
		<ShortDescription>MidPoint over period</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MIDPRICE -->
	<FinancialFunction>
		<Abbreviation>MIDPRICE</Abbreviation>
		<CamelCaseName>MidPrice</CamelCaseName>
		<ShortDescription>Midpoint Price over period</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MIN -->
	<FinancialFunction>
		<Abbreviation>MIN</Abbreviation>
		<CamelCaseName>Min</CamelCaseName>
		<ShortDescription>Lowest value over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MININDEX -->
	<FinancialFunction>
		<Abbreviation>MININDEX</Abbreviation>
		<CamelCaseName>MinIndex</CamelCaseName>
		<ShortDescription>Index of lowest value over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outInteger</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MINMAX -->
	<FinancialFunction>
		<Abbreviation>MINMAX</Abbreviation>
		<CamelCaseName>MinMax</CamelCaseName>
		<ShortDescription>Lowest and highest values over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMin</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outMax</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MINMAXINDEX -->
	<FinancialFunction>
		<Abbreviation>MINMAXINDEX</Abbreviation>
		<CamelCaseName>MinMaxIndex</CamelCaseName>
		<ShortDescription>Indexes of lowest and highest values over a specified period</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outMinIdx</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Integer Array</Type>
				<Name>outMaxIdx</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MINUS_DI -->
	<FinancialFunction>
		<Abbreviation>MINUS_DI</Abbreviation>
		<CamelCaseName>MinusDI</CamelCaseName>
		<ShortDescription>Minus Directional Indicator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MINUS_DM -->
	<FinancialFunction>
		<Abbreviation>MINUS_DM</Abbreviation>
		<CamelCaseName>MinusDM</CamelCaseName>
		<ShortDescription>Minus Directional Movement</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MOM -->
	<FinancialFunction>
		<Abbreviation>MOM</Abbreviation>
		<CamelCaseName>Mom</CamelCaseName>
		<ShortDescription>Momentum</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- MULT -->
	<FinancialFunction>
		<Abbreviation>MULT</Abbreviation>
		<CamelCaseName>Mult</CamelCaseName>
		<ShortDescription>Vector Arithmetic Mult</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- NATR -->
	<FinancialFunction>
		<Abbreviation>NATR</Abbreviation>
		<CamelCaseName>Natr</CamelCaseName>
		<ShortDescription>Normalized Average True Range</ShortDescription>
		<GroupId>Volatility Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- OBV -->
	<FinancialFunction>
		<Abbreviation>OBV</Abbreviation>
		<CamelCaseName>Obv</CamelCaseName>
		<ShortDescription>On Balance Volume</ShortDescription>
		<GroupId>Volume Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Volume</Type>
				<Name>Volume</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- PLUS_DI -->
	<FinancialFunction>
		<Abbreviation>PLUS_DI</Abbreviation>
		<CamelCaseName>PlusDI</CamelCaseName>
		<ShortDescription>Plus Directional Indicator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- PLUS_DM -->
	<FinancialFunction>
		<Abbreviation>PLUS_DM</Abbreviation>
		<CamelCaseName>PlusDM</CamelCaseName>
		<ShortDescription>Plus Directional Movement</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- PPO -->
	<FinancialFunction>
		<Abbreviation>PPO</Abbreviation>
		<CamelCaseName>Ppo</CamelCaseName>
		<ShortDescription>Percentage Price Oscillator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast Period</Name>
				<ShortDescription>Number of period for the fast MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>12</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow Period</Name>
				<ShortDescription>Number of period for the slow MA</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>26</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>MA Type</Name>
				<ShortDescription>Type of Moving Average</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ROC -->
	<FinancialFunction>
		<Abbreviation>ROC</Abbreviation>
		<CamelCaseName>Roc</CamelCaseName>
		<ShortDescription>Rate of change : ((price/prevPrice)-1)*100</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ROCP -->
	<FinancialFunction>
		<Abbreviation>ROCP</Abbreviation>
		<CamelCaseName>RocP</CamelCaseName>
		<ShortDescription>Rate of change Percentage: (price-prevPrice)/prevPrice</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ROCR -->
	<FinancialFunction>
		<Abbreviation>ROCR</Abbreviation>
		<CamelCaseName>RocR</CamelCaseName>
		<ShortDescription>Rate of change ratio: (price/prevPrice)</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ROCR100 -->
	<FinancialFunction>
		<Abbreviation>ROCR100</Abbreviation>
		<CamelCaseName>RocR100</CamelCaseName>
		<ShortDescription>Rate of change ratio 100 scale: (price/prevPrice)*100</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>10</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- RSI -->
	<FinancialFunction>
		<Abbreviation>RSI</Abbreviation>
		<CamelCaseName>Rsi</CamelCaseName>
		<ShortDescription>Relative Strength Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SAR -->
	<FinancialFunction>
		<Abbreviation>SAR</Abbreviation>
		<CamelCaseName>Sar</CamelCaseName>
		<ShortDescription>Parabolic SAR</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Acceleration Factor</Name>
				<ShortDescription>Acceleration Factor used up to the Maximum value</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>2.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Maximum</Name>
				<ShortDescription>Acceleration Factor Maximum value</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>2.000000e-1</SuggestedStart>
					<SuggestedEnd>4.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SAREXT -->
	<FinancialFunction>
		<Abbreviation>SAREXT</Abbreviation>
		<CamelCaseName>SarExt</CamelCaseName>
		<ShortDescription>Parabolic SAR - Extended</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Start Value</Name>
				<ShortDescription>Start value and direction. 0 for Auto, &gt;0 for Long, &lt;0 for Short</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>-3.000000e+37</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>0.000000e+0</SuggestedStart>
					<SuggestedEnd>0.000000e+0</SuggestedEnd>
					<SuggestedIncrement>0.000000e+0</SuggestedIncrement>
				</Range>
				<DefaultValue>0.000000e+0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Offset on Reverse</Name>
				<ShortDescription>Percent offset added/removed to initial stop on short/long reversal</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>1.500000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>0.000000e+0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Init Long</Name>
				<ShortDescription>Acceleration Factor initial value for the Long direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>1.900000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Long</Name>
				<ShortDescription>Acceleration Factor for the Long direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>2.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Max Long</Name>
				<ShortDescription>Acceleration Factor maximum value for the Long direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>2.000000e-1</SuggestedStart>
					<SuggestedEnd>4.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-1</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Init Short</Name>
				<ShortDescription>Acceleration Factor initial value for the Short direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>1.900000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Short</Name>
				<ShortDescription>Acceleration Factor for the Short direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>2.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-2</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>AF Max Short</Name>
				<ShortDescription>Acceleration Factor maximum value for the Short direction</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>4</Precision>
					<SuggestedStart>2.000000e-1</SuggestedStart>
					<SuggestedEnd>4.000000e-1</SuggestedEnd>
					<SuggestedIncrement>1.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>2.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SIN -->
	<FinancialFunction>
		<Abbreviation>SIN</Abbreviation>
		<CamelCaseName>Sin</CamelCaseName>
		<ShortDescription>Vector Trigonometric Sin</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SINH -->
	<FinancialFunction>
		<Abbreviation>SINH</Abbreviation>
		<CamelCaseName>Sinh</CamelCaseName>
		<ShortDescription>Vector Trigonometric Sinh</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SMA -->
	<FinancialFunction>
		<Abbreviation>SMA</Abbreviation>
		<CamelCaseName>Sma</CamelCaseName>
		<ShortDescription>Simple Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SQRT -->
	<FinancialFunction>
		<Abbreviation>SQRT</Abbreviation>
		<CamelCaseName>Sqrt</CamelCaseName>
		<ShortDescription>Vector Square Root</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- STDDEV -->
	<FinancialFunction>
		<Abbreviation>STDDEV</Abbreviation>
		<CamelCaseName>StdDev</CamelCaseName>
		<ShortDescription>Standard Deviation</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Deviations</Name>
				<ShortDescription>Nb of deviations</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>-3.000000e+37</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>-2.000000e+0</SuggestedStart>
					<SuggestedEnd>2.000000e+0</SuggestedEnd>
					<SuggestedIncrement>2.000000e-1</SuggestedIncrement>
				</Range>
				<DefaultValue>1.000000e+0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- STOCH -->
	<FinancialFunction>
		<Abbreviation>STOCH</Abbreviation>
		<CamelCaseName>Stoch</CamelCaseName>
		<ShortDescription>Stochastic</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast-K Period</Name>
				<ShortDescription>Time period for building the Fast-K line</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow-K Period</Name>
				<ShortDescription>Smoothing for making the Slow-K line. Usually set to 3</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>3</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow-K MA</Name>
				<ShortDescription>Type of Moving Average for Slow-K</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow-D Period</Name>
				<ShortDescription>Smoothing for making the Slow-D line</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>3</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Slow-D MA</Name>
				<ShortDescription>Type of Moving Average for Slow-D</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outSlowK</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outSlowD</Name>
				<Flags>
					<Flag>Dashed Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- STOCHF -->
	<FinancialFunction>
		<Abbreviation>STOCHF</Abbreviation>
		<CamelCaseName>StochF</CamelCaseName>
		<ShortDescription>Stochastic Fast</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Fast-K Period</Name>
				<ShortDescription>Time period for building the Fast-K line</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast-D Period</Name>
				<ShortDescription>Smoothing for making the Fast-D line. Usually set to 3</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>3</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast-D MA</Name>
				<ShortDescription>Type of Moving Average for Fast-D</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outFastK</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outFastD</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- STOCHRSI -->
	<FinancialFunction>
		<Abbreviation>STOCHRSI</Abbreviation>
		<CamelCaseName>StochRsi</CamelCaseName>
		<ShortDescription>Stochastic Relative Strength Index</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<Flags>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast-K Period</Name>
				<ShortDescription>Time period for building the Fast-K line</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast-D Period</Name>
				<ShortDescription>Smoothing for making the Fast-D line. Usually set to 3</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>3</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Fast-D MA</Name>
				<ShortDescription>Type of Moving Average for Fast-D</ShortDescription>
				<Type>MA Type</Type>
				<DefaultValue>0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outFastK</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outFastD</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SUB -->
	<FinancialFunction>
		<Abbreviation>SUB</Abbreviation>
		<CamelCaseName>Sub</CamelCaseName>
		<ShortDescription>Vector Arithmetic Subtraction</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal0</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal1</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- SUM -->
	<FinancialFunction>
		<Abbreviation>SUM</Abbreviation>
		<CamelCaseName>Sum</CamelCaseName>
		<ShortDescription>Summation</ShortDescription>
		<GroupId>Math Operators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- T3 -->
	<FinancialFunction>
		<Abbreviation>T3</Abbreviation>
		<CamelCaseName>T3</CamelCaseName>
		<ShortDescription>Triple Exponential Moving Average (T3)</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
			<Flag>Unstable Period</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Volume Factor</Name>
				<ShortDescription>Volume Factor</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>0.000000e+0</Minimum>
					<Maximum>1.000000e+0</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>1.000000e-2</SuggestedStart>
					<SuggestedEnd>1.000000e+0</SuggestedEnd>
					<SuggestedIncrement>5.000000e-2</SuggestedIncrement>
				</Range>
				<DefaultValue>7.000000e-1</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TAN -->
	<FinancialFunction>
		<Abbreviation>TAN</Abbreviation>
		<CamelCaseName>Tan</CamelCaseName>
		<ShortDescription>Vector Trigonometric Tan</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TANH -->
	<FinancialFunction>
		<Abbreviation>TANH</Abbreviation>
		<CamelCaseName>Tanh</CamelCaseName>
		<ShortDescription>Vector Trigonometric Tanh</ShortDescription>
		<GroupId>Math Transform</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TEMA -->
	<FinancialFunction>
		<Abbreviation>TEMA</Abbreviation>
		<CamelCaseName>Tema</CamelCaseName>
		<ShortDescription>Triple Exponential Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TRANGE -->
	<FinancialFunction>
		<Abbreviation>TRANGE</Abbreviation>
		<CamelCaseName>TrueRange</CamelCaseName>
		<ShortDescription>True Range</ShortDescription>
		<GroupId>Volatility Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TRIMA -->
	<FinancialFunction>
		<Abbreviation>TRIMA</Abbreviation>
		<CamelCaseName>Trima</CamelCaseName>
		<ShortDescription>Triangular Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TRIX -->
	<FinancialFunction>
		<Abbreviation>TRIX</Abbreviation>
		<CamelCaseName>Trix</CamelCaseName>
		<ShortDescription>1-day Rate-Of-Change (ROC) of a Triple Smooth EMA</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TSF -->
	<FinancialFunction>
		<Abbreviation>TSF</Abbreviation>
		<CamelCaseName>Tsf</CamelCaseName>
		<ShortDescription>Time Series Forecast</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- TYPPRICE -->
	<FinancialFunction>
		<Abbreviation>TYPPRICE</Abbreviation>
		<CamelCaseName>TypPrice</CamelCaseName>
		<ShortDescription>Typical Price</ShortDescription>
		<GroupId>Price Transform</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- ULTOSC -->
	<FinancialFunction>
		<Abbreviation>ULTOSC</Abbreviation>
		<CamelCaseName>UltOsc</CamelCaseName>
		<ShortDescription>Ultimate Oscillator</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>First Period</Name>
				<ShortDescription>Number of bars for 1st period.</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>7</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Second Period</Name>
				<ShortDescription>Number of bars fro 2nd period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Third Period</Name>
				<ShortDescription>Number of bars for 3rd period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>28</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- VAR -->
	<FinancialFunction>
		<Abbreviation>VAR</Abbreviation>
		<CamelCaseName>Variance</CamelCaseName>
		<ShortDescription>Variance</ShortDescription>
		<GroupId>Statistic Functions</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>1</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>5</DefaultValue>
			</OptionalInputArgument>
			<OptionalInputArgument>
				<Name>Deviations</Name>
				<ShortDescription>Nb of deviations</ShortDescription>
				<Type>Double</Type>
				<Range>
					<Minimum>-3.000000e+37</Minimum>
					<Maximum>3.000000e+37</Maximum>
					<Precision>2</Precision>
					<SuggestedStart>-2.000000e+0</SuggestedStart>
					<SuggestedEnd>2.000000e+0</SuggestedEnd>
					<SuggestedIncrement>2.000000e-1</SuggestedIncrement>
				</Range>
				<DefaultValue>1.000000e+0</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- WCLPRICE -->
	<FinancialFunction>
		<Abbreviation>WCLPRICE</Abbreviation>
		<CamelCaseName>WclPrice</CamelCaseName>
		<ShortDescription>Weighted Close Price</ShortDescription>
		<GroupId>Price Transform</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- WILLR -->
	<FinancialFunction>
		<Abbreviation>WILLR</Abbreviation>
		<CamelCaseName>WillR</CamelCaseName>
		<ShortDescription>Williams&apos; %R</ShortDescription>
		<GroupId>Momentum Indicators</GroupId>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>High</Type>
				<Name>High</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Low</Type>
				<Name>Low</Name>
			</RequiredInputArgument>
			<RequiredInputArgument>
				<Type>Close</Type>
				<Name>Close</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>14</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


	<!-- WMA -->
	<FinancialFunction>
		<Abbreviation>WMA</Abbreviation>
		<CamelCaseName>Wma</CamelCaseName>
		<ShortDescription>Weighted Moving Average</ShortDescription>
		<GroupId>Overlap Studies</GroupId>
		<Flags>
			<Flag>Overlap</Flag>
		</Flags>
		<RequiredInputArguments>
			<RequiredInputArgument>
				<Type>Double Array</Type>
				<Name>inReal</Name>
			</RequiredInputArgument>
		</RequiredInputArguments>
		<OptionalInputArguments>
			<OptionalInputArgument>
				<Name>Time Period</Name>
				<ShortDescription>Number of period</ShortDescription>
				<Type>Integer</Type>
				<Range>
					<Minimum>2</Minimum>
					<Maximum>100000</Maximum>
					<SuggestedStart>100000</SuggestedStart>
					<SuggestedEnd>100000</SuggestedEnd>
					<SuggestedIncrement>100000</SuggestedIncrement>
				</Range>
				<DefaultValue>30</DefaultValue>
			</OptionalInputArgument>
		</OptionalInputArguments>
		<OutputArguments>
			<OutputArgument>
				<Type>Double Array</Type>
				<Name>outReal</Name>
				<Flags>
					<Flag>Line</Flag>
				</Flags>
			</OutputArgument>
		</OutputArguments>
	</FinancialFunction>


</FinancialFunctions>
