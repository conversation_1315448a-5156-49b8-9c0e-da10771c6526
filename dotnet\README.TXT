* TA-Lib for .NET *

How to use TA-Lib with .NET?
============================
Copy and distribute TA-Lib-Core.dll in
the same directory as your application.

The assembly is located in ta-lib\dotnet\src\Release.

It requires the .NET 2.0 Framework.

The assembly is not shared (or strongly named) and
consequently does not require to be added to the GAC.

The assembly does not contain language specific feature and
can be used with any .NET language (C#,VB.NET,C++,J#...).

The assembly can be used with Mono as well.

How to rebuild the TA-LIB assembly?
===================================
Using Visual Studio 2008 open ta-lib\dotnet\src\TA-Lib.sln and
rebuild TA-Lib-Core.

There is no support for older Visual Studio version.

Can I re-distribute the TA-Lib assembly?
========================================
TA-Lib can be distributed with commercial software
for no fee. You must respect the LICENSE.TXT included
in this package.

Don't forget to include a copy of the License when you
are redistributing.

How to uninstall the library?
=============================
Just delete the ta-lib directory and assembly.
TA-LIB does not touch your system settings, 
the GAC or the registry.

Comments/Help
=============
 http://ta-lib.org
 http://tadoc.org
