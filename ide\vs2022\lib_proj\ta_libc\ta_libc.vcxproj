﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="cdd|Win32">
      <Configuration>cdd</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cdr|Win32">
      <Configuration>cdr</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cmd|Win32">
      <Configuration>cmd</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cmr|Win32">
      <Configuration>cmr</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <ProjectGuid>{B7BA9CEF-8430-4BCB-8EEE-5C351800A021}</ProjectGuid>
    <RootNamespace>ta_libc</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>11.0.50727.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'">
    <OutDir>.\..\..\..\..\lib\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <NMakeBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>del $(OutDir)ta_libc_$(Configuration).lib</NMakeCleanCommandLine>
    <NMakeOutput>$(OutDir)ta_libc_$(Configuration).lib</NMakeOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'">
    <OutDir>.\..\..\..\..\lib\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <NMakeBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>del $(OutDir)ta_libc_$(Configuration).lib</NMakeCleanCommandLine>
    <NMakeOutput>$(OutDir)ta_libc_$(Configuration).lib</NMakeOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'">
    <OutDir>.\..\..\..\..\lib\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <NMakeBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>del $(OutDir)ta_libc_$(Configuration).lib</NMakeCleanCommandLine>
    <NMakeOutput>$(OutDir)ta_libc_$(Configuration).lib</NMakeOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'">
    <OutDir>.\..\..\..\..\lib\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <NMakeBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>lib /OUT:$(OutDir)ta_libc_$(Configuration).lib $(OutDir)ta_abstract_$(Configuration).lib $(OutDir)ta_common_$(Configuration).lib $(OutDir)ta_func_$(Configuration).lib</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>del $(OutDir)ta_libc_$(Configuration).lib</NMakeCleanCommandLine>
    <NMakeOutput>$(OutDir)ta_libc_$(Configuration).lib</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\include\ta_abstract.h" />
    <ClInclude Include="..\..\..\..\include\ta_common.h" />
    <ClInclude Include="..\..\..\..\include\ta_defs.h" />
    <ClInclude Include="..\..\..\..\include\ta_func.h" />
    <ClInclude Include="..\..\..\..\include\ta_libc.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ta_abstract\ta_abstract.vcxproj">
      <Project>{fde2592b-df64-4308-b644-92e79e071986}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ta_common\ta_common.vcxproj">
      <Project>{e3ab3a1b-0d33-4a51-a4f7-69ee5c079115}</Project>
    </ProjectReference>
    <ProjectReference Include="..\ta_func\ta_func.vcxproj">
      <Project>{b9dbb4a6-c675-4486-b916-0b0dcc049bb2}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>