[![Discord chat](https://img.shields.io/discord/1038616996062953554.svg?logo=discord&style=flat-square)](https://discord.gg/Erb6SwsVbH)

[![main nightly tests](https://github.com/TA-Lib/ta-lib/actions/workflows/main-nightly-tests.yml/badge.svg)](https://github.com/TA-Lib/ta-lib/actions/workflows/main-nightly-tests.yml) [![dev nightly tests](https://github.com/TA-Lib/ta-lib/actions/workflows/dev-nightly-tests.yml/badge.svg)](https://github.com/TA-Lib/ta-lib/actions/workflows/dev-nightly-tests.yml)

# TA-Lib - Technical Analysis Library
This is now the official home for C/C++ TA-Lib (instead of SourceForge).

More info [https://ta-lib.org](https://ta-lib.org)

# You want a new TA Function implemented?
First step is to document the algorithm, with a sample of input/output in the [ta-lib-proposal-drafts]( https://github.com/TA-Lib/ta-lib-proposal-drafts ) repos.
