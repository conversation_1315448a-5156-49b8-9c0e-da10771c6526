/* TA-LIB Copyright (c) 1999-2025, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, E<PERSON>EMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* List of contributors:
 *
 *  Initial  Name/description
 *  -------------------------------------------------------------------
 *  MF       Mario Fortier
 *
 * Change history:
 *
 *  MMDDYY BY     Description
 *  -------------------------------------------------------------------
 *  010125 MF     Initial Version - Go language support for TA-Lib
 */

/*
 * This file contains the Go language code generation functions for TA-Lib.
 * It follows the same pattern as gen_rust.c to maintain consistency.
 */

struct WriteGoModLinesParams {
   FileHandle *out;
   int writeExports; // 0 to write imports, 1 to write exports
};

// Forward declarations
static void generateGoFunction(FILE* out, const TA_FuncInfo* funcInfo, FILE* logicTmp);
static void generateGoLookbackFunction(FILE* out, const TA_FuncInfo* funcInfo);
static void generateGoMainFunction(FILE* out, const TA_FuncInfo* funcInfo);
static void generateGoFunctionSignature(FILE* out, const TA_FuncInfo* funcInfo);
static void generateGoFunctionBody(FILE* out, const TA_FuncInfo* funcInfo);
static void generateOptionalParameters(FILE* out, const TA_FuncInfo* funcInfo, int isLookback);
static void generateOptionalParametersCGOCall(FILE* out, const TA_FuncInfo* funcInfo);
static void generateInputParameters(FILE* out, const TA_FuncInfo* funcInfo);
static void generateOutputParameters(FILE* out, const TA_FuncInfo* funcInfo);
static const char* mapCTypeToGo(const char* cType);
static void generateInputValidation(FILE* out, const TA_FuncInfo* funcInfo);

static void writeGoModLines(const TA_FuncInfo* funcInfo, void* opaqueData)
{
   struct WriteGoModLinesParams* params = (struct WriteGoModLinesParams*)opaqueData;
   FILE* out = params->out->file;
   char funcNameLower[256];
   int i;

   if (!ggo_installed)
      return;

   // Convert function name to lowercase for Go package naming
   for (i = 0; i < sizeof(funcNameLower) - 1 && funcInfo->name[i]; i++) {
      funcNameLower[i] = tolower(funcInfo->name[i]);
   }
   funcNameLower[i] = '\0';

   if (params->writeExports) {
      // Write function exports
      fprintf(out, "// %s - %s\n", funcInfo->name, funcInfo->hint ? funcInfo->hint : "Technical Analysis Function");
      fprintf(out, "func %s(", funcInfo->camelCaseName);
      
      // Add input parameters (simplified for now)
      fprintf(out, "inReal []float64");
      
      // Add optional parameters if any
      if (funcInfo->nbOptInput > 0) {
         fprintf(out, ", optParams ...interface{}");
      }
      
      fprintf(out, ") ([]float64, error) {\n");
      fprintf(out, "\treturn %s_cgo(inReal, optParams...)\n", funcNameLower);
      fprintf(out, "}\n\n");
   } else {
      // Write internal CGO function declarations
      fprintf(out, "func %s_cgo(inReal []float64, optParams ...interface{}) ([]float64, error)\n", funcNameLower);
   }
}

void writeGoMod(void)
{
   // Update the go/src/talib/functions.go file.
   struct WriteGoModLinesParams params;
   char buffer[500];
   FileHandle* out;

   if (!ggo_installed)
      return;

   // Create the main Go module file
#define FILE_GO_FUNCTIONS ".." PATH_SEPARATOR "go" PATH_SEPARATOR "src" PATH_SEPARATOR "talib" PATH_SEPARATOR "functions.go"
#define FILE_GO_FUNCTIONS_TEMPLATE ".." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_abstract" PATH_SEPARATOR "templates" PATH_SEPARATOR "ta_functions.go.template"

   out = fileOpen(FILE_GO_FUNCTIONS,
                  FILE_GO_FUNCTIONS_TEMPLATE,
                  FILE_WRITE | WRITE_ON_CHANGE_ONLY);

   if (!out) {
      printf("Warning: Cannot create Go functions file\n");
      return;
   }

   params.out = out;
   
   // First pass: write internal CGO declarations
   params.writeExports = 0;
   TA_ForEachFunc(writeGoModLines, &params);
   
   // Second pass: write public function exports
   params.writeExports = 1;
   TA_ForEachFunc(writeGoModLines, &params);

   fileClose(out);
}

void genGoCodePhase2(const TA_FuncInfo* funcInfo)
{
   // Each TA function gets its own .go file generated.
   // A common header/footer is provided by the template file.
   FILE* logicTmp;
   char buffer[500];
   char funcNameLower[256];
   int idx, again;
   static int firstTime = 1;
   int ret;
   int i;

#if defined(GO_SINGLE_FUNC)
   if (strcmp(funcInfo->name, GO_SINGLE_FUNC) != 0)
      return;
#endif

   if (!ggo_installed)
      return;

   // Convert filename to lowercase
   for (i = 0; i < sizeof(funcNameLower) - 1 && funcInfo->name[i]; i++) {
      funcNameLower[i] = tolower(funcInfo->name[i]);
   }
   funcNameLower[i] = '\0';
   strcat(funcNameLower, ".go");

#define FILE_GO_FUNC_TEMPLATE ".." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_abstract" PATH_SEPARATOR "templates" PATH_SEPARATOR "ta_x.go.template"

   FileHandle* out = fileOpen(ta_fs_path(5, "..", "go", "src", "talib", funcNameLower),
                              FILE_GO_FUNC_TEMPLATE,
                              FILE_WRITE | WRITE_ON_CHANGE_ONLY);

   if (!out) {
      printf("Warning: Cannot create Go function file for %s\n", funcInfo->name);
      return;
   }

   /* Clean-up just in case. */
   fileDelete(ta_fs_path(3, "..", "temp", "go_logic.tmp"));

   // Use mcpp to preprocess the C source with Go-specific defines
#ifdef _MSC_VER
   sprintf(buffer, "%s -c -+ -z -P -I.." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_common -I.." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_abstract -I.." PATH_SEPARATOR "include -D _GO .." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_func" PATH_SEPARATOR "TA_%s.c >>.." PATH_SEPARATOR "temp" PATH_SEPARATOR "go_logic.tmp ", gmcpp_exec, funcInfo->name);
   ret = system(buffer);
#else
   sprintf(buffer,
           "%s -@compat -+ -z -P -I.." PATH_SEPARATOR "src" PATH_SEPARATOR "ta_common -I.." PATH_SEPARATOR "src"
           PATH_SEPARATOR "ta_abstract -I.." PATH_SEPARATOR "include -D _GO .." PATH_SEPARATOR "src" PATH_SEPARATOR
           "ta_func" PATH_SEPARATOR "ta_%s.c | sed '/^#include/d' >> .." PATH_SEPARATOR "temp" PATH_SEPARATOR
           "go_logic.tmp ", gmcpp_exec, funcInfo->name);
   ret = system(buffer);
#endif

   /* Write the output of the C pre-processor to the Go file. */
   init_gToOpen(ta_fs_path(3, "..", "temp", "go_logic.tmp"), NULL);
   logicTmp = fopen(gToOpen, "r");
   if (logicTmp) {
      // Generate Go function with CGO bindings
      generateGoFunction(out->file, funcInfo, logicTmp);
      fclose(logicTmp);
   }

   fileClose(out);

   /* Clean-up */
   fileDelete(ta_fs_path(3, "..", "temp", "go_logic.tmp"));

#undef FILE_GO_FUNC_TEMPLATE
}

// Generate complete Go function with CGO bindings
static void generateGoFunction(FILE* out, const TA_FuncInfo* funcInfo, FILE* logicTmp)
{
   fprintf(out, "// %s - %s\n", funcInfo->name, funcInfo->hint ? funcInfo->hint : "Technical Analysis Function");
   fprintf(out, "// Generated from TA-Lib C source\n");
   fprintf(out, "// Function: %s, Group: %s\n", funcInfo->name, funcInfo->group);
   fprintf(out, "// Inputs: %u, OptInputs: %u, Outputs: %u\n\n",
           funcInfo->nbInput, funcInfo->nbOptInput, funcInfo->nbOutput);

   // Generate lookback function
   generateGoLookbackFunction(out, funcInfo);

   // Generate main function
   generateGoMainFunction(out, funcInfo);
}

// Generate lookback function
static void generateGoLookbackFunction(FILE* out, const TA_FuncInfo* funcInfo)
{
   fprintf(out, "// %sLookback returns the number of elements needed for %s calculation\n",
           funcInfo->camelCaseName, funcInfo->name);
   fprintf(out, "func %sLookback(", funcInfo->camelCaseName);

   // Generate optional parameters for lookback
   generateOptionalParameters(out, funcInfo, 1); // 1 = for lookback function

   fprintf(out, ") int {\n");

   // Generate CGO call for lookback
   fprintf(out, "\tretCode := C.TA_%s_Lookback(", funcInfo->name);
   generateOptionalParametersCGOCall(out, funcInfo);
   fprintf(out, ")\n");
   fprintf(out, "\treturn int(retCode)\n");
   fprintf(out, "}\n\n");
}

// Generate main function
static void generateGoMainFunction(FILE* out, const TA_FuncInfo* funcInfo)
{
   generateGoFunctionSignature(out, funcInfo);
   generateGoFunctionBody(out, funcInfo);
}

static void generateGoFunctionSignature(FILE* out, const TA_FuncInfo* funcInfo)
{
   fprintf(out, "// %s calculates %s\n", funcInfo->camelCaseName,
           funcInfo->hint ? funcInfo->hint : funcInfo->name);
   fprintf(out, "// Inputs: %u, OptInputs: %u, Outputs: %u\n",
           funcInfo->nbInput, funcInfo->nbOptInput, funcInfo->nbOutput);
   fprintf(out, "func %s(", funcInfo->camelCaseName);

   // Input parameters
   generateInputParameters(out, funcInfo);

   // Optional parameters
   generateOptionalParameters(out, funcInfo, 0); // 0 = not lookback function

   fprintf(out, ") ");

   // Output parameters
   generateOutputParameters(out, funcInfo);

   fprintf(out, " {\n");
}

static void generateGoFunctionBody(FILE* out, const TA_FuncInfo* funcInfo)
{
   // Input validation
   generateInputValidation(out, funcInfo);

   // Memory management and CGO call
   fprintf(out, "\t// Prepare input data\n");
   fprintf(out, "\tstartIdx := 0\n");
   fprintf(out, "\tendIdx := len(inReal) - 1\n");
   fprintf(out, "\tinPtr, inPinner := pinSlice(inReal)\n");
   fprintf(out, "\tif inPinner != nil {\n");
   fprintf(out, "\t\tdefer inPinner.Unpin()\n");
   fprintf(out, "\t}\n\n");

   // Allocate outputs based on number of outputs
   fprintf(out, "\t// Allocate output(s)\n");
   fprintf(out, "\tmaxOutput := len(inReal)\n");

   if (funcInfo->nbOutput == 1) {
      fprintf(out, "\toutReal, outPtr, outPinner := allocateOutput(maxOutput)\n");
      fprintf(out, "\tif outPinner != nil {\n");
      fprintf(out, "\t\tdefer outPinner.Unpin()\n");
      fprintf(out, "\t}\n\n");
   } else {
      // Multiple outputs
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         fprintf(out, "\toutReal%u, outPtr%u, outPinner%u := allocateOutput(maxOutput)\n", i, i, i);
         fprintf(out, "\tif outPinner%u != nil {\n", i);
         fprintf(out, "\t\tdefer outPinner%u.Unpin()\n", i);
         fprintf(out, "\t}\n");
      }
      fprintf(out, "\n");
   }

   // CGO call
   fprintf(out, "\t// CGO call\n");
   fprintf(out, "\tvar outBegIdx, outNBElement C.int\n");
   fprintf(out, "\tretCode := C.TA_%s(\n", funcInfo->name);
   fprintf(out, "\t\tC.int(startIdx),\n");
   fprintf(out, "\t\tC.int(endIdx),\n");
   fprintf(out, "\t\tinPtr,\n");

   // Add optional parameters
   if (funcInfo->nbOptInput > 0) {
      fprintf(out, "\t\t");
      generateOptionalParametersCGOCall(out, funcInfo);
      fprintf(out, ",\n");
   }

   fprintf(out, "\t\t&outBegIdx,\n");
   fprintf(out, "\t\t&outNBElement,\n");

   // Add output pointers
   if (funcInfo->nbOutput == 1) {
      fprintf(out, "\t\toutPtr,\n");
   } else {
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         fprintf(out, "\t\toutPtr%u,\n", i);
      }
   }

   fprintf(out, "\t)\n\n");

   // Check return code and return results
   fprintf(out, "\t// Check return code\n");
   fprintf(out, "\tif retCode != C.TA_SUCCESS {\n");
   if (funcInfo->nbOutput == 1) {
      fprintf(out, "\t\treturn nil, convertRetCode(retCode)\n");
   } else {
      fprintf(out, "\t\treturn ");
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         if (i > 0) fprintf(out, ", ");
         fprintf(out, "nil");
      }
      fprintf(out, ", convertRetCode(retCode)\n");
   }
   fprintf(out, "\t}\n\n");

   // Return results
   fprintf(out, "\t// Return result(s)\n");
   fprintf(out, "\tresultLen := int(outNBElement)\n");
   fprintf(out, "\tif resultLen <= 0 {\n");
   if (funcInfo->nbOutput == 1) {
      fprintf(out, "\t\treturn []float64{}, nil\n");
   } else {
      fprintf(out, "\t\treturn ");
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         if (i > 0) fprintf(out, ", ");
         fprintf(out, "[]float64{}");
      }
      fprintf(out, ", nil\n");
   }
   fprintf(out, "\t}\n");

   if (funcInfo->nbOutput == 1) {
      fprintf(out, "\treturn outReal[:resultLen], nil\n");
   } else {
      fprintf(out, "\treturn ");
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         if (i > 0) fprintf(out, ", ");
         fprintf(out, "outReal%u[:resultLen]", i);
      }
      fprintf(out, ", nil\n");
   }

   fprintf(out, "}\n\n");
}

static void generateInputValidation(FILE* out, const TA_FuncInfo* funcInfo)
{
   fprintf(out, "\t// Input validation\n");

   // For now, assume single real input - will enhance based on actual input types
   if (funcInfo->nbInput > 0) {
      fprintf(out, "\tif err := validateInputData(inReal, 1, \"inReal\"); err != nil {\n");
      fprintf(out, "\t\treturn nil, err\n");
      fprintf(out, "\t}\n");
   }

   // Validate indices
   fprintf(out, "\tif err := validateRange(0, len(inReal)-1, len(inReal)); err != nil {\n");
   fprintf(out, "\t\treturn nil, err\n");
   fprintf(out, "\t}\n\n");
}

// Generate optional parameters for function signature
static void generateOptionalParameters(FILE* out, const TA_FuncInfo* funcInfo, int isLookback)
{
   if (funcInfo->nbOptInput > 0) {
      // For now, use a simplified approach with variadic interface{}
      // In a full implementation, we'd generate specific parameter types
      if (isLookback) {
         fprintf(out, "optParams ...interface{}");
      } else {
         fprintf(out, ", optParams ...interface{}");
      }
   }
}

// Generate CGO call for optional parameters
static void generateOptionalParametersCGOCall(FILE* out, const TA_FuncInfo* funcInfo)
{
   if (funcInfo->nbOptInput > 0) {
      // For now, use default values - in full implementation, process optParams
      fprintf(out, "/* TODO: process optParams - using defaults for now */");

      // Generate default values based on common TA-Lib patterns
      for (unsigned int i = 0; i < funcInfo->nbOptInput; i++) {
         if (i > 0) fprintf(out, ", ");
         // Most common optional parameter is period, default to 14
         fprintf(out, "C.int(14)");
      }
   }
}

// Generate input parameters for function signature
static void generateInputParameters(FILE* out, const TA_FuncInfo* funcInfo)
{
   // For now, simplified - assume most functions take real data
   // In full implementation, would analyze actual input parameter types
   if (funcInfo->nbInput > 0) {
      fprintf(out, "inReal []float64");

      // If multiple inputs, add them
      for (unsigned int i = 1; i < funcInfo->nbInput; i++) {
         fprintf(out, ", inReal%u []float64", i);
      }
   }
}

// Generate output parameters for return type
static void generateOutputParameters(FILE* out, const TA_FuncInfo* funcInfo)
{
   if (funcInfo->nbOutput == 1) {
      fprintf(out, "([]float64, error)");
   } else if (funcInfo->nbOutput > 1) {
      fprintf(out, "(");
      for (unsigned int i = 0; i < funcInfo->nbOutput; i++) {
         if (i > 0) fprintf(out, ", ");
         fprintf(out, "[]float64");
      }
      fprintf(out, ", error)");
   } else {
      fprintf(out, "error");
   }
}
