﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="src">
      <UniqueIdentifier>{dc1f7dd3-4937-4ea6-b050-a88bd2a48fa0}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="src\ta_abstract">
      <UniqueIdentifier>{40f7916e-5ea4-40e8-8070-552b521d90f7}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ta_abstract\frames">
      <UniqueIdentifier>{e5723058-7624-47c3-bc84-8220c5cc49c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ta_abstract\tables">
      <UniqueIdentifier>{c9630e95-eae0-430d-896c-9e50f37e8e7e}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{5a6b3160-a21f-4e60-b907-2af35c8ff1af}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_abstract.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_def_ui.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_func_api.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_group_idx.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\frames\ta_frame.c">
      <Filter>src\ta_abstract\frames</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_a.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_b.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_c.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_d.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_e.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_f.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_g.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_h.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_i.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_j.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_k.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_l.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_m.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_n.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_o.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_p.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_q.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_r.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_s.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_t.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_u.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_v.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_w.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_x.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_y.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_z.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\src\ta_abstract\ta_def_ui.h">
      <Filter>src\ta_abstract</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\ta_abstract\ta_frame_priv.h">
      <Filter>src\ta_abstract</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\ta_abstract\frames\ta_frame.h">
      <Filter>src\ta_abstract\frames</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\include\ta_abstract.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\include\ta_common.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\ta_common\ta_memory.h">
      <Filter>include</Filter>
    </ClInclude>
  </ItemGroup>
</Project>