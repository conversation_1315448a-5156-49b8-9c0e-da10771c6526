/* TA-LIB Copyright (c) 1999-2025, <PERSON>
 * All rights reserved.
 *
 * This file demonstrates what a generated Go function should look like
 * for the Simple Moving Average (SMA) function.
 */

package talib

/*
#cgo CFLAGS: -I../../../include
#cgo LDFLAGS: -L../../../lib -lta_lib
#include <stdlib.h>
#include <string.h>
#include "ta_libc.h"
*/
import "C"
import (
	"errors"
	"runtime"
	"unsafe"
)

// SMALookback returns the number of elements needed for SMA calculation
func SMALookback(period int) int {
	retCode := C.TA_SMA_Lookback(C.int(period))
	return int(retCode)
}

// SMA calculates Simple Moving Average
// Inputs: 1, OptInputs: 1, Outputs: 1
func SMA(inReal []float64, period int) ([]float64, error) {
	// Input validation
	if err := validateInputData(inReal, 1, "inReal"); err != nil {
		return nil, err
	}
	if err := validateRange(0, len(inReal)-1, len(inReal)); err != nil {
		return nil, err
	}
	if err := validatePeriod(period, 1); err != nil {
		return nil, err
	}

	// Prepare input data
	startIdx := 0
	endIdx := len(inReal) - 1
	inPtr, inPinner := pinSlice(inReal)
	if inPinner != nil {
		defer inPinner.Unpin()
	}

	// Allocate output
	maxOutput := len(inReal)
	outReal, outPtr, outPinner := allocateOutput(maxOutput)
	if outPinner != nil {
		defer outPinner.Unpin()
	}

	// CGO call
	var outBegIdx, outNBElement C.int
	retCode := C.TA_SMA(
		C.int(startIdx),
		C.int(endIdx),
		inPtr,
		C.int(period),
		&outBegIdx,
		&outNBElement,
		outPtr,
	)

	// Check return code
	if retCode != C.TA_SUCCESS {
		return nil, convertRetCode(retCode)
	}

	// Return result
	resultLen := int(outNBElement)
	if resultLen <= 0 {
		return []float64{}, nil
	}
	return outReal[:resultLen], nil
}

// Example of a function with multiple outputs (like MACD)
// MACDLookback returns the number of elements needed for MACD calculation
func MACDLookback(fastPeriod, slowPeriod, signalPeriod int) int {
	retCode := C.TA_MACD_Lookback(C.int(fastPeriod), C.int(slowPeriod), C.int(signalPeriod))
	return int(retCode)
}

// MACD calculates Moving Average Convergence/Divergence
// Inputs: 1, OptInputs: 3, Outputs: 3
func MACD(inReal []float64, fastPeriod, slowPeriod, signalPeriod int) ([]float64, []float64, []float64, error) {
	// Input validation
	if err := validateInputData(inReal, 1, "inReal"); err != nil {
		return nil, nil, nil, err
	}
	if err := validateRange(0, len(inReal)-1, len(inReal)); err != nil {
		return nil, nil, nil, err
	}

	// Prepare input data
	startIdx := 0
	endIdx := len(inReal) - 1
	inPtr, inPinner := pinSlice(inReal)
	if inPinner != nil {
		defer inPinner.Unpin()
	}

	// Allocate outputs
	maxOutput := len(inReal)
	outMACD, outMACDPtr, outMACDPinner := allocateOutput(maxOutput)
	if outMACDPinner != nil {
		defer outMACDPinner.Unpin()
	}
	outMACDSignal, outMACDSignalPtr, outMACDSignalPinner := allocateOutput(maxOutput)
	if outMACDSignalPinner != nil {
		defer outMACDSignalPinner.Unpin()
	}
	outMACDHist, outMACDHistPtr, outMACDHistPinner := allocateOutput(maxOutput)
	if outMACDHistPinner != nil {
		defer outMACDHistPinner.Unpin()
	}

	// CGO call
	var outBegIdx, outNBElement C.int
	retCode := C.TA_MACD(
		C.int(startIdx),
		C.int(endIdx),
		inPtr,
		C.int(fastPeriod),
		C.int(slowPeriod),
		C.int(signalPeriod),
		&outBegIdx,
		&outNBElement,
		outMACDPtr,
		outMACDSignalPtr,
		outMACDHistPtr,
	)

	// Check return code
	if retCode != C.TA_SUCCESS {
		return nil, nil, nil, convertRetCode(retCode)
	}

	// Return results
	resultLen := int(outNBElement)
	if resultLen <= 0 {
		return []float64{}, []float64{}, []float64{}, nil
	}
	return outMACD[:resultLen], outMACDSignal[:resultLen], outMACDHist[:resultLen], nil
}
