<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="perl"
	ProjectGUID="{C0E4CC9C-8719-4FDB-8B7B-40456BD7EEEB}"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory=".\..\..\..\temp\perl\wrap"
			IntermediateDirectory=".\..\..\..\temp\perl\wrap"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine=""
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\..\..\..\temp\perl\wrap/perl.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="1"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\..\..\c\include,$(PERL5_INCLUDE)"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;MSWIN32;_CONSOLE;NO_STRICT;PERL_MSVCRT_READFIX;PERL_CAPI"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\..\..\..\temp\perl\wrap/perl.pch"
				AssemblerListingLocation=".\..\..\..\temp\perl\wrap/"
				ObjectFile=".\..\..\..\temp\perl\wrap/"
				ProgramDataBaseFileName=".\..\..\..\temp\perl\wrap/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="..\..\..\..\c\lib\ta_libc_cdr.lib odbc32.lib odbccp32.lib wininet.lib $(PERL5_INCLUDE)/$(PERL5_LIB)"
				OutputFile="..\..\..\lib\perl\ta.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary=".\..\..\..\temp\perl\wrap/ta.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\..\..\..\temp\perl\wrap/perl.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="..\..\..\temp\perl\wrap\ta_libc_wrap.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="..\..\..\..\c\include\ta_abstract.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_common.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_data.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_defs.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_func.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_libc.h"
				>
			</File>
			<File
				RelativePath="..\..\..\..\c\include\ta_pm.h"
				>
			</File>
		</Filter>
		<Filter
			Name="test_perl"
			>
			<File
				RelativePath="..\..\..\src\tools\test_perl\runtests.pl"
				>
			</File>
			<File
				RelativePath="..\..\..\src\tools\test_perl\ta_abstract.t"
				>
			</File>
			<File
				RelativePath="..\..\..\src\tools\test_perl\ta_common.t"
				>
			</File>
			<File
				RelativePath="..\..\..\src\tools\test_perl\ta_defs.t"
				>
			</File>
			<File
				RelativePath="..\..\..\src\tools\test_perl\ta_func.t"
				>
			</File>
		</Filter>
		<File
			RelativePath="..\..\..\src\interface\perl.pm"
			>
		</File>
		<File
			RelativePath="..\..\..\src\interface\ta_func.swg"
			>
		</File>
		<File
			RelativePath="..\..\..\src\interface\ta_libc.perl.swg"
			>
		</File>
		<File
			RelativePath="..\..\..\src\interface\ta_libc.swg"
			>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					CommandLine="echo In order to function correctly, please ensure the following environment variables are correctly set:&#x0D;&#x0A;echo PERL5_INCLUDE: %PERL5_INCLUDE%&#x0D;&#x0A;echo PERL5_LIB: %PERL5_LIB%&#x0D;&#x0A;echo Make also sure that swig and perl are on search path:&#x0D;&#x0A;echo %PATH%&#x0D;&#x0A;echo on&#x0D;&#x0A;swig -perl -module &quot;Finance::TA&quot; -proxy -Fmicrosoft -o ..\..\..\temp\perl\wrap\$(InputName)_wrap.c -I..\..\..\..\c\include -I$(InputDir) $(InputPath)&#x0D;&#x0A;perl -pe &quot;s/^\x25(OWNER|ITERATORS)/our \x25\1/&quot; ..\..\..\temp\perl\wrap\TA.pm &gt;..\..\..\lib\perl\Finance\TA.pm&#x0D;&#x0A;type ..\..\..\src\interface\perl.pm &gt;&gt;..\..\..\lib\perl\Finance\TA.pm&#x0D;&#x0A;"
					AdditionalDependencies="..\..\..\src\interface\perl.pm;"
					Outputs="..\..\..\temp\perl\wrap\$(InputName)_wrap.c;..\..\..\temp\perl\wrap\TA.pm"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
