﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="cdd|Win32">
      <Configuration>cdd</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cdr|Win32">
      <Configuration>cdr</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cmd|Win32">
      <Configuration>cmd</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="cmr|Win32">
      <Configuration>cmr</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{271698C6-3436-4D9B-87DB-F865325B00C5}</ProjectGuid>
    <RootNamespace>ta_regtest</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>11.0.50727.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'">
    <OutDir>.\..\..\..\..\bin\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'">
    <OutDir>.\..\..\..\..\bin\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'">
    <OutDir>.\..\..\..\..\bin\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'">
    <OutDir>.\..\..\..\..\bin\</OutDir>
    <IntDir>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='cmr|Win32'">
    <Midl>
      <TypeLibraryName>.\..\..\..\..\bin/ta_regtest.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>.\..\..\..\..\src\ta_common;.\..\..\..\..\src\tools\ta_regtest;.\..\..\..\..\src\ta_func;.\..\..\..\..\include;.\..\..\..\..\src\ta_common\trio;.\..\..\..\..\src\ta_common\mt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NO_DEBUG;NDEBUG;WIN32;_MBCS;_CONSOLE;QT_THREAD_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <ExceptionHandling />
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</AssemblerListingLocation>
      <ObjectFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ProgramDataBaseFileName>
      <XMLDocumentationFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</XMLDocumentationFileName>
      <BrowseInformationFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</BrowseInformationFile>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>ta_libc_$(Configuration).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName)_$(Configuration).exe</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\..\..\..\..\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).intermediate.manifest</ManifestFile>
      <ProgramDatabaseFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Manifest>
      <OutputManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest</OutputManifestFile>
    </Manifest>
    <ManifestResourceCompile>
      <ResourceOutputFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest.res</ResourceOutputFileName>
    </ManifestResourceCompile>
    <Xdcmake>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).xml</OutputFile>
    </Xdcmake>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Message>Make a copy named ta_regtest.exe</Message>
      <Command>copy $(OutDir)$(ProjectName)_$(Configuration).exe $(OutDir)$(ProjectName).exe
copy $(OutDir)$(ProjectName)_$(Configuration).exe $(OutDir)$(ProjectName)_vs2005.exe
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='cmd|Win32'">
    <Midl>
      <TypeLibraryName>.\..\..\..\..\bin/ta_regtest.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.\..\..\..\..\src\ta_common;.\..\..\..\..\src\tools\ta_regtest;.\..\..\..\..\src\ta_func;.\..\..\..\..\include;.\..\..\..\..\src\ta_common\trio;.\..\..\..\..\src\ta_common\mt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>TA_DEBUG;_DEBUG;WIN32;_MBCS;_CONSOLE;QT_THREAD_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <ExceptionHandling />
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</AssemblerListingLocation>
      <ObjectFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ProgramDataBaseFileName>
      <XMLDocumentationFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</XMLDocumentationFileName>
      <BrowseInformation>true</BrowseInformation>
      <BrowseInformationFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</BrowseInformationFile>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>ta_libc_$(Configuration).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName)_$(Configuration).exe</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\..\..\..\..\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).intermediate.manifest</ManifestFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Manifest>
      <OutputManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest</OutputManifestFile>
    </Manifest>
    <ManifestResourceCompile>
      <ResourceOutputFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest.res</ResourceOutputFileName>
    </ManifestResourceCompile>
    <Xdcmake>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).xml</OutputFile>
    </Xdcmake>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Message>Make a copy named ta_regtest.exe</Message>
      <Command>copy $(OutDir)$(ProjectName)_$(Configuration).exe $(OutDir)$(ProjectName).exe</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='cdd|Win32'">
    <Midl>
      <TypeLibraryName>.\..\..\..\..\bin/ta_regtest.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.\..\..\..\..\src\ta_common;.\..\..\..\..\src\tools\ta_regtest;.\..\..\..\..\src\ta_func;.\..\..\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>TA_DEBUG;_DEBUG;WIN32;_MBCS;_CONSOLE;QT_THREAD_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <ExceptionHandling />
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</AssemblerListingLocation>
      <ObjectFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ProgramDataBaseFileName>
      <XMLDocumentationFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</XMLDocumentationFileName>
      <BrowseInformation>true</BrowseInformation>
      <BrowseInformationFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</BrowseInformationFile>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>ta_libc_$(Configuration).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName)_$(Configuration).exe</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\..\..\..\..\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).intermediate.manifest</ManifestFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Manifest>
      <OutputManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest</OutputManifestFile>
    </Manifest>
    <ManifestResourceCompile>
      <ResourceOutputFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest.res</ResourceOutputFileName>
    </ManifestResourceCompile>
    <Xdcmake>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).xml</OutputFile>
    </Xdcmake>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Message>Make a copy named ta_regtest.exe</Message>
      <Command>copy $(OutDir)$(ProjectName)_$(Configuration).exe $(OutDir)$(ProjectName).exe</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='cdr|Win32'">
    <Midl>
      <TypeLibraryName>.\..\..\..\..\bin/ta_regtest.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>.\..\..\..\..\src\ta_common;.\..\..\..\..\src\tools\ta_regtest;.\..\..\..\..\src\ta_func;.\..\..\..\..\include;.\..\..\..\..\src\ta_common\trio;.\..\..\..\..\src\ta_common\mt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NO_DEBUG;NDEBUG;WIN32;_MBCS;_CONSOLE;QT_THREAD_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <ExceptionHandling />
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</AssemblerListingLocation>
      <ObjectFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</ProgramDataBaseFileName>
      <XMLDocumentationFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</XMLDocumentationFileName>
      <BrowseInformationFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/</BrowseInformationFile>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>ta_libc_$(Configuration).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName)_$(Configuration).exe</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <AdditionalLibraryDirectories>.\..\..\..\..\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).intermediate.manifest</ManifestFile>
      <ProgramDatabaseFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).pdb</ProgramDatabaseFile>
      <SubSystem>Console</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
    <Manifest>
      <OutputManifestFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest</OutputManifestFile>
    </Manifest>
    <ManifestResourceCompile>
      <ResourceOutputFileName>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).embed.manifest.res</ResourceOutputFileName>
    </ManifestResourceCompile>
    <Xdcmake>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).xml</OutputFile>
    </Xdcmake>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\..\..\temp\$(Configuration)\$(ProjectName)/$(ProjectName)_$(Configuration).bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Message>Make a copy named ta_regtest.exe</Message>
      <Command>copy $(OutDir)$(ProjectName)_$(Configuration).exe $(OutDir)$(ProjectName).exe</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\src\tools\ta_regtest\ta_error_number.h" />
    <ClInclude Include="..\..\..\..\src\tools\ta_regtest\ta_test_func.h" />
    <ClInclude Include="..\..\..\..\src\tools\ta_regtest\ta_test_priv.h" />
    <ClInclude Include="..\..\..\..\include\ta_libc.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_regtest.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_avgdev.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_imi.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\test_abstract.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\test_data.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\test_internals.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\test_util.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_1in_1out.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_1in_2out.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_adx.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_bbands.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_candlestick.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_ma.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_macd.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_minmax.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_mom.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_per_ema.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_per_hl.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_per_hlc.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_per_hlcv.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_per_ohlc.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_po.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_rsi.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_sar.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_stddev.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_stoch.c" />
    <ClCompile Include="..\..\..\..\src\tools\ta_regtest\ta_test_func\test_trange.c" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\gen_code\gen_code.vcxproj">
      <Project>{79d18abf-c93a-441a-a22b-d58813bb2701}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\ta_abstract\ta_abstract.vcxproj">
      <Project>{fde2592b-df64-4308-b644-92e79e071986}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\ta_common\ta_common.vcxproj">
      <Project>{e3ab3a1b-0d33-4a51-a4f7-69ee5c079115}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\ta_func\ta_func.vcxproj">
      <Project>{b9dbb4a6-c675-4486-b916-0b0dcc049bb2}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\ta_libc\ta_libc.vcxproj">
      <Project>{b7ba9cef-8430-4bcb-8eee-5c351800a021}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>