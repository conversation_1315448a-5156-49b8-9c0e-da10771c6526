/* TA-LIB Copyright (c) 1999-2025, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, E<PERSON>EMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 * Important:
 *  This file is automatically generated by the utility gen_code.
 *  Any modifications will be lost on next execution of gen_code.
 *
 *  All TA-Lib functions are available through this package.
 */

// Package talib provides Go bindings for TA-Lib (Technical Analysis Library)
//
// TA-Lib is widely used by trading software developers requiring to perform
// technical analysis of financial market data.
//
// This package provides production-quality CGO bindings with:
// - Zero precision loss compared to the C library
// - Comprehensive error handling and validation
// - Memory safety with proper pointer management
// - Thread safety for concurrent usage
//
// Example usage:
//
//	import "github.com/ta-lib/ta-lib-go/talib"
//
//	// Simple Moving Average
//	result, err := talib.SMA(prices, 14)
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	// Relative Strength Index
//	rsi, err := talib.RSI(prices, 14)
//	if err != nil {
//		log.Fatal(err)
//	}
package talib

/*
#cgo CFLAGS: -I../../../include
#cgo LDFLAGS: -L../../../lib -lta_lib
#include <stdlib.h>
#include <string.h>
#include "ta_libc.h"
*/
import "C"
import (
	"errors"
	"runtime"
	"unsafe"
)

// Initialize initializes the TA-Lib library
// This must be called before using any TA-Lib functions
func Initialize() error {
	retCode := C.TA_Initialize()
	if retCode != C.TA_SUCCESS {
		return convertRetCode(retCode)
	}
	return nil
}

// Shutdown shuts down the TA-Lib library
// This should be called when done using TA-Lib functions
func Shutdown() error {
	retCode := C.TA_Shutdown()
	if retCode != C.TA_SUCCESS {
		return convertRetCode(retCode)
	}
	return nil
}

// Version returns the TA-Lib version string
func Version() string {
	return C.GoString(C.TA_GetVersionString())
}

// Common parameter validation functions
func validateInputData(data []float64, minLength int, paramName string) error {
	if data == nil {
		return errors.New(paramName + " cannot be nil")
	}
	if len(data) < minLength {
		return errors.New(paramName + " must have at least " + string(rune(minLength)) + " elements")
	}
	return nil
}

func validatePeriod(period int, minPeriod int) error {
	if period < minPeriod {
		return errors.New("period must be at least " + string(rune(minPeriod)))
	}
	return nil
}

func validateRange(startIdx, endIdx, dataLen int) error {
	if startIdx < 0 {
		return errors.New("startIdx cannot be negative")
	}
	if endIdx < startIdx {
		return errors.New("endIdx cannot be less than startIdx")
	}
	if endIdx >= dataLen {
		return errors.New("endIdx exceeds data length")
	}
	return nil
}

// Memory management helpers
func pinSlice(data []float64) (*C.double, *runtime.Pinner) {
	if len(data) == 0 {
		return nil, nil
	}
	pinner := &runtime.Pinner{}
	pinner.Pin(&data[0])
	return (*C.double)(unsafe.Pointer(&data[0])), pinner
}

func allocateOutput(size int) ([]float64, *C.double, *runtime.Pinner) {
	if size <= 0 {
		return nil, nil, nil
	}
	output := make([]float64, size)
	pinner := &runtime.Pinner{}
	pinner.Pin(&output[0])
	return output, (*C.double)(unsafe.Pointer(&output[0])), pinner
}

// Generated function declarations and implementations
%%%GENCODE%%%

/***************/
/* End of File */
/***************/
