module github.com/ta-lib/ta-lib-go

go 1.21

// TA-Lib Go Bindings
//
// This module provides production-quality CGO bindings for TA-Lib
// (Technical Analysis Library) with zero precision loss and comprehensive
// error handling.
//
// Requirements:
// - Go 1.21 or later (for runtime.Pinner support)
// - CGO enabled (CGO_ENABLED=1)
// - TA-Lib C library installed
//
// Installation:
//   go get github.com/ta-lib/ta-lib-go
//
// Usage:
//   import "github.com/ta-lib/ta-lib-go/talib"
//
// For more information, visit: https://ta-lib.org/
