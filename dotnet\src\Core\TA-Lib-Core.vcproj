<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="TA-Lib-Core"
	ProjectGUID="{667AF01A-5614-4446-9DCC-744D27906E5B}"
	RootNamespace="TALibCore"
	Keyword="ManagedCProj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			ManagedExtensions="3"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\c\src\ta_common; ..\..\..\dotnet\src\Core;..\..\..\c\include"
				PreprocessorDefinitions="WIN32;_DEBUG;CODE_ANALYSIS "
				MinimalRebuild="false"
				BasicRuntimeChecks="0"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				WarnAsError="true"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
				EnablePREfast="false"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(NoInherit)"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				AssemblyDebug="1"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
				KeyFile="$(ProjectDir)..\key.snk"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
				EnableFxCop="false"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			ManagedExtensions="3"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="..\..\..\c\src\ta_common; ..\..\..\dotnet\src\Core;..\..\..\c\include"
				PreprocessorDefinitions="WIN32;NDEBUG"
				MinimalRebuild="false"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(NoInherit)"
				LinkIncremental="1"
				GenerateDebugInformation="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
				KeyFile="$(ProjectDir)..\key.snk"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug SubArray|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			ManagedExtensions="3"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\c\src\ta_common; ..\..\..\dotnet\src\Core;..\..\..\c\include"
				PreprocessorDefinitions="WIN32;_DEBUG;CODE_ANALYSIS;USE_SUBARRAY"
				MinimalRebuild="false"
				BasicRuntimeChecks="0"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				WarnAsError="true"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				EnablePREfast="false"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(NoInherit)"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				AssemblyDebug="1"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
				KeyFile="$(ProjectDir)..\key.snk"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
				EnableFxCop="false"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release SubArray|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			ManagedExtensions="3"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="..\..\..\c\src\ta_common; ..\..\..\dotnet\src\Core;..\..\..\c\include"
				PreprocessorDefinitions="WIN32;NDEBUG;USE_SUBARRAY"
				MinimalRebuild="false"
				RuntimeLibrary="2"
				UsePrecompiledHeader="2"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="$(NoInherit)"
				LinkIncremental="1"
				GenerateDebugInformation="true"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="0"
				KeyFile="$(ProjectDir)..\key.snk"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
		<AssemblyReference
			RelativePath="System.dll"
			AssemblyName="System, Version=*******, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
			MinFrameworkVersion="131072"
		/>
		<AssemblyReference
			RelativePath="System.Data.dll"
			AssemblyName="System.Data, Version=*******, PublicKeyToken=b77a5c561934e089, processorArchitecture=x86"
			MinFrameworkVersion="131072"
		/>
		<AssemblyReference
			RelativePath="System.XML.dll"
			AssemblyName="System.Xml, Version=*******, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
			MinFrameworkVersion="131072"
		/>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\AssemblyInfo.cpp"
				>
			</File>
			<File
				RelativePath=".\Stdafx.cpp"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug SubArray|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release SubArray|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\TA-Lib-Core.cpp"
				>
			</File>
			<File
				RelativePath="..\..\..\c\src\ta_func\ta_utility.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
						CompileAs="2"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
						CompileAs="2"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug SubArray|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
						CompileAs="2"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release SubArray|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
						CompileAs="2"
					/>
				</FileConfiguration>
			</File>
			<Filter
				Name="ta_func"
				>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ACCBANDS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ACOS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_AD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ADD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ADOSC.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ADX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ADXR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_APO.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_AROON.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_AROONOSC.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ASIN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ATAN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ATR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_AVGPRICE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_AVGDEV.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_BBANDS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_BETA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_BOP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CCI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL2CROWS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3BLACKCROWS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3INSIDE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3LINESTRIKE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3OUTSIDE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3STARSINSOUTH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDL3WHITESOLDIERS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLABANDONEDBABY.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLADVANCEBLOCK.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLBELTHOLD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLBREAKAWAY.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLCLOSINGMARUBOZU.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLCONCEALBABYSWALL.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLCOUNTERATTACK.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLDARKCLOUDCOVER.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLDOJI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLDOJISTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLDRAGONFLYDOJI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLENGULFING.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLEVENINGDOJISTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLEVENINGSTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLGAPSIDESIDEWHITE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLGRAVESTONEDOJI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHAMMER.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHANGINGMAN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHARAMI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHARAMICROSS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHIGHWAVE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHIKKAKE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHIKKAKEMOD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLHOMINGPIGEON.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLIDENTICAL3CROWS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLINNECK.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLINVERTEDHAMMER.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLKICKING.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLKICKINGBYLENGTH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLLADDERBOTTOM.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLLONGLEGGEDDOJI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLLONGLINE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLMARUBOZU.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLMATCHINGLOW.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLMATHOLD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLMORNINGDOJISTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLMORNINGSTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLONNECK.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLPIERCING.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLRICKSHAWMAN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLRISEFALL3METHODS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSEPARATINGLINES.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSHOOTINGSTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSHORTLINE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSPINNINGTOP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSTALLEDPATTERN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLSTICKSANDWICH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLTAKURI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLTASUKIGAP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLTHRUSTING.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLTRISTAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLUNIQUE3RIVER.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLUPSIDEGAP2CROWS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CDLXSIDEGAP3METHODS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CEIL.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CMO.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_CORREL.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_COS.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_COSH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_DEMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_DIV.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_DX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_EMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_EXP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_FLOOR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_DCPERIOD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_DCPHASE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_PHASOR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_SINE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_TRENDLINE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_HT_TRENDMODE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_IMI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_KAMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LINEARREG.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LINEARREG_ANGLE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LINEARREG_INTERCEPT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LINEARREG_SLOPE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_LOG10.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MACD.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MACDEXT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MACDFIX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MAMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MAVP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MAX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MAXINDEX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MEDPRICE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MFI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MIDPOINT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MIDPRICE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MIN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MININDEX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MINMAX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MINMAXINDEX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MINUS_DI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MINUS_DM.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MOM.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_MULT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_NATR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_OBV.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_PLUS_DI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_PLUS_DM.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_PPO.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ROC.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ROCP.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ROCR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ROCR100.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_RSI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SAREXT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SIN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SINH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SQRT.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_STDDEV.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_STOCH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_STOCHF.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_STOCHRSI.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SUB.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_SUM.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_T3.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TAN.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TANH.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TEMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TRANGE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TRIMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TRIX.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TSF.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_TYPPRICE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_ULTOSC.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_VAR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_WCLPRICE.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_WILLR.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\..\..\c\src\ta_func\ta_WMA.c">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release SubArray|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							UsePrecompiledHeader="0"
							CompileAs="2"/>
					</FileConfiguration>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\resource.h"
				>
			</File>
			<File
				RelativePath=".\Stdafx.h"
				>
			</File>
			<File
				RelativePath=".\TA-Lib-Core.h"
				>
			</File>
			<File
				RelativePath="..\..\..\c\src\ta_func\ta_utility.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
			<File
				RelativePath=".\app.ico"
				>
			</File>
			<File
				RelativePath=".\app.rc"
				>
			</File>
		</Filter>
		<File
			RelativePath=".\ReadMe.txt"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
