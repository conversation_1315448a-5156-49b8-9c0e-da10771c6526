/* TA-LIB Copyright (c) 1999-2025, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, E<PERSON>EMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* Important:
 *  This file is automatically generated by the utility gen_code.
 *  Any modifications will be lost on next execution of gen_code.
 *
 *  Modifications should instead be done with the "C" source file
 *  in ta-lib\src\ta_func
 */

 impl core {
 fn mult_lookback(
{
   return 0;
}
 fn mult( int startIdx,
 int endIdx,
double inReal0[],
double inReal1[],
mut outBegIdx,
mut outNBElement,
double outReal[],
)
{
   int outIdx;
   int i;
 if( startIdx < 0 )
 return  RetCode.OutOfRangeStartIndex ;
 if( (endIdx < 0) || (endIdx < startIdx))
 return  RetCode.OutOfRangeEndIndex ;
   for( i=startIdx, outIdx=0; i <= endIdx; i++, outIdx++ )
   {
      outReal[outIdx] = inReal0[i]*inReal1[i];
   }
   outNBElement.value  = outIdx;
   outBegIdx.value  = startIdx;
   return  RetCode.Success ;
}
 fn mult_s( int startIdx,
 int endIdx,
 {
 int outIdx;
 int i;
 if( startIdx < 0 )
 return  RetCode.OutOfRangeStartIndex ;
 if( (endIdx < 0) || (endIdx < startIdx))
 return  RetCode.OutOfRangeEndIndex ;
 for( i=startIdx, outIdx=0; i <= endIdx; i++, outIdx++ )
 {
 outReal[outIdx] = inReal0[i]*inReal1[i];
 }
 outNBElement.value  = outIdx;
 outBegIdx.value  = startIdx;
 return  RetCode.Success ;
 }
 }
/* Generated */ 

/***************/
/* End of File */
/***************/
