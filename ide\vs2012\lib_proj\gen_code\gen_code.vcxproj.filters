﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="src">
      <UniqueIdentifier>{6972204d-d4c0-4d34-82a8-dfd169e76b15}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="src\tools">
      <UniqueIdentifier>{2f67b6cb-464d-430a-b009-e746a1b4fe78}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\tools\gen_code">
      <UniqueIdentifier>{5944d68a-2ee8-415c-8469-5e3279a6742c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ta_abstract">
      <UniqueIdentifier>{c4c1a9f2-0c6d-4b42-bc9b-9ea0c0622ef1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ta_abstract\tables">
      <UniqueIdentifier>{621d8fd4-2262-4f3d-8b38-93d0a03fa2a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{30b2747f-fb3e-4a82-9fdd-6435be1d6948}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\src\tools\gen_code\gen_code.c">
      <Filter>src\tools\gen_code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_abstract.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\ta_def_ui.c">
      <Filter>src\ta_abstract</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_a.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_b.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_c.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_d.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_e.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_f.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_g.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_h.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_i.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_j.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_k.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_l.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_m.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_n.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_o.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_p.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_q.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_r.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_s.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_t.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_u.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_v.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_w.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_x.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_y.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ta_abstract\tables\table_z.c">
      <Filter>src\ta_abstract\tables</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\src\ta_abstract\ta_def_ui.h">
      <Filter>src\ta_abstract</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\include\ta_common.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\ta_common\ta_pragma.h">
      <Filter>include</Filter>
    </ClInclude>
  </ItemGroup>
</Project>