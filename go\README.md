# TA-Lib Go Bindings

Production-quality Go bindings for TA-Lib (Technical Analysis Library) with zero precision loss and comprehensive error handling.

## Features

- **Zero Precision Loss**: Direct CGO calls to C library maintain numerical accuracy
- **Memory Safety**: Proper pointer management with runtime.Pinner
- **Thread Safety**: Safe for concurrent usage
- **Comprehensive Error Handling**: All TA-Lib return codes mapped to Go errors
- **Type Safety**: Strong typing with validation
- **Performance**: Optimized CGO bindings with minimal overhead

## Requirements

- Go 1.21 or later (for runtime.Pinner support)
- CGO enabled (`CGO_ENABLED=1`)
- TA-Lib C library installed

## Installation

```bash
go get github.com/ta-lib/ta-lib-go
```

## Usage

```go
package main

import (
    "fmt"
    "log"
    
    "github.com/ta-lib/ta-lib-go/talib"
)

func main() {
    // Initialize TA-Lib
    if err := talib.Initialize(); err != nil {
        log.Fatal(err)
    }
    defer talib.Shutdown()
    
    // Sample price data
    prices := []float64{
        44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 45.92,
        45.73, 46.16, 46.47, 46.09, 46.03, 46.83, 47.69, 47.54,
        47.00, 47.24, 47.90, 47.73, 47.61, 47.60, 48.06, 47.71,
    }
    
    // Calculate Simple Moving Average
    sma, err := talib.SMA(prices, 10)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("SMA(10): %v\n", sma)
    
    // Calculate RSI
    rsi, err := talib.RSI(prices, 14)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("RSI(14): %v\n", rsi)
    
    // Calculate MACD
    macd, signal, histogram, err := talib.MACD(prices, 12, 26, 9)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("MACD: %v\n", macd)
    fmt.Printf("Signal: %v\n", signal)
    fmt.Printf("Histogram: %v\n", histogram)
}
```

## Available Functions

This package provides Go bindings for all 200+ TA-Lib functions, including:

### Overlap Studies
- SMA, EMA, WMA, DEMA, TEMA, TRIMA
- KAMA, MAMA, T3
- Bollinger Bands, Parabolic SAR
- Hilbert Transform functions

### Momentum Indicators
- RSI, MACD, Stochastic
- Williams %R, Rate of Change
- Commodity Channel Index (CCI)
- Money Flow Index (MFI)

### Volume Indicators
- Chaikin A/D Line, Chaikin A/D Oscillator
- On Balance Volume (OBV)
- Average Directional Index (ADX)

### Volatility Indicators
- Average True Range (ATR)
- Normalized Average True Range (NATR)
- True Range (TRANGE)

### Price Transform
- Average Price, Median Price
- Typical Price, Weighted Close Price

### Cycle Indicators
- Hilbert Transform - Dominant Cycle Period
- Hilbert Transform - Dominant Cycle Phase
- Hilbert Transform - Phasor Components

### Pattern Recognition
- All candlestick pattern recognition functions
- 60+ candlestick patterns supported

### Math Transform
- Vector arithmetic operations
- Trigonometric functions
- Statistical functions

### Math Operators
- ADD, SUB, MULT, DIV
- MIN, MAX, MINMAX
- SUM, SQRT, etc.

## Error Handling

All functions return appropriate errors for:
- Invalid input parameters
- Insufficient data
- Memory allocation failures
- TA-Lib internal errors

```go
result, err := talib.SMA(data, period)
if err != nil {
    switch err.(type) {
    case talib.RetCode:
        // Handle specific TA-Lib error codes
        fmt.Printf("TA-Lib error: %v\n", err)
    default:
        // Handle validation errors
        fmt.Printf("Validation error: %v\n", err)
    }
    return
}
```

## Performance Considerations

- Use appropriate slice capacities to minimize allocations
- Consider using lookback functions to determine required data length
- CGO calls have ~40ns overhead per function call
- Memory is automatically managed with runtime.Pinner

## Building

To build applications using these bindings:

```bash
# Ensure CGO is enabled
export CGO_ENABLED=1

# Build your application
go build your_app.go
```

## Contributing

This package is automatically generated from TA-Lib C source code. 
Contributions should be made to the TA-Lib project at https://github.com/TA-Lib/ta-lib

## License

Same as TA-Lib: BSD 3-Clause License

## Links

- [TA-Lib Official Website](https://ta-lib.org/)
- [TA-Lib GitHub](https://github.com/TA-Lib/ta-lib)
- [TA-Lib Documentation](https://ta-lib.org/function.html)
