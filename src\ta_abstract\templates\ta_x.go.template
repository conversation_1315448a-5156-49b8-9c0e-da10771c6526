/* TA-LIB Copyright (c) 1999-2025, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, E<PERSON>EMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 * Important:
 *  This file is automatically generated by the utility gen_code.
 *  Any modifications will be lost on next execution of gen_code.
 *
 *  Modifications should instead be done with the "C" source file
 *  in ta-lib\src\ta_func
 */

package talib

/*
#cgo CFLAGS: -I../../../include
#cgo LDFLAGS: -L../../../lib -lta_lib
#include <stdlib.h>
#include <string.h>
#include "ta_libc.h"
*/
import "C"
import (
	"errors"
	"runtime"
	"unsafe"
)

// RetCode represents TA-Lib return codes
type RetCode int

const (
	Success RetCode = iota
	LibNotInitialize
	BadParam
	AllocErr
	GroupNotFound
	FuncNotFound
	InvalidHandle
	InvalidParamHolder
	InvalidParamHolderType
	InvalidParamFunction
	InputNotAllFloat
	InputNotAllInteger
	OutputNotAllFloat
	OutputNotAllInteger
	OutOfRangeStartIndex
	OutOfRangeEndIndex
	InvalidListType
	BadObject
	NotSupported
	InternalError
	UnknownErr
)

// Error returns the string representation of a RetCode
func (r RetCode) Error() string {
	switch r {
	case Success:
		return "Success"
	case LibNotInitialize:
		return "Library not initialized"
	case BadParam:
		return "Bad parameter"
	case AllocErr:
		return "Allocation error"
	case GroupNotFound:
		return "Group not found"
	case FuncNotFound:
		return "Function not found"
	case InvalidHandle:
		return "Invalid handle"
	case InvalidParamHolder:
		return "Invalid parameter holder"
	case InvalidParamHolderType:
		return "Invalid parameter holder type"
	case InvalidParamFunction:
		return "Invalid parameter function"
	case InputNotAllFloat:
		return "Input not all float"
	case InputNotAllInteger:
		return "Input not all integer"
	case OutputNotAllFloat:
		return "Output not all float"
	case OutputNotAllInteger:
		return "Output not all integer"
	case OutOfRangeStartIndex:
		return "Out of range start index"
	case OutOfRangeEndIndex:
		return "Out of range end index"
	case InvalidListType:
		return "Invalid list type"
	case BadObject:
		return "Bad object"
	case NotSupported:
		return "Not supported"
	case InternalError:
		return "Internal error"
	default:
		return "Unknown error"
	}
}

// convertRetCode converts C TA_RetCode to Go RetCode
func convertRetCode(cRetCode C.TA_RetCode) RetCode {
	return RetCode(int(cRetCode))
}

// validateSlice validates input slice parameters
func validateSlice(data []float64, name string) error {
	if data == nil {
		return errors.New(name + " cannot be nil")
	}
	if len(data) == 0 {
		return errors.New(name + " cannot be empty")
	}
	return nil
}

// validateIndices validates start and end indices
func validateIndices(startIdx, endIdx, dataLen int) error {
	if startIdx < 0 {
		return errors.New("startIdx cannot be negative")
	}
	if endIdx < startIdx {
		return errors.New("endIdx cannot be less than startIdx")
	}
	if endIdx >= dataLen {
		return errors.New("endIdx cannot be greater than or equal to data length")
	}
	return nil
}

// sliceToDoublePtr converts Go float64 slice to C double pointer with memory pinning
func sliceToDoublePtr(data []float64) (*C.double, *runtime.Pinner) {
	if len(data) == 0 {
		return nil, nil
	}
	
	pinner := &runtime.Pinner{}
	pinner.Pin(&data[0])
	return (*C.double)(unsafe.Pointer(&data[0])), pinner
}

// allocateOutputSlice allocates and returns output slice with proper size
func allocateOutputSlice(maxSize int) ([]float64, *C.double, *runtime.Pinner) {
	if maxSize <= 0 {
		return nil, nil, nil
	}
	
	output := make([]float64, maxSize)
	pinner := &runtime.Pinner{}
	pinner.Pin(&output[0])
	cPtr := (*C.double)(unsafe.Pointer(&output[0]))
	
	return output, cPtr, pinner
}

%%%GENCODE%%%

/***************/
/* End of File */
/***************/
