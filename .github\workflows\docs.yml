name: Documentation

on:
  push:
    branches: [main]
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  workflow_dispatch:

permissions:
  contents: write

jobs:
  diff:
    runs-on: [ubuntu-latest]
    outputs:
      isDoc: ${{ steps.diff.outputs.isDoc }}
    steps:
      - uses: actions/checkout@v4
      - name: Detect Changes
        uses: "./.github/actions/diffs"
        id: diff

  deploy:
    runs-on: ubuntu-latest
    if: github.event.repository.fork == false
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: pip install mkdocs-material
      - run: pip install mkdocs-git-revision-date-localized-plugin
      - run: pip install mkdocs-minify-plugin
      - run: mkdocs gh-deploy --force
